// Test script to verify navigation and chat history clearing
console.log('🧪 TipTop Navigation Test Script Loaded');

// Function to test button existence and functionality
function testButtonFunctionality() {
  console.log('🧪 Testing button functionality...');

  const button = document.getElementById('tiptop-button');
  if (button) {
    console.log('✅ Button found in DOM');
    console.log('🧪 Button styles:', {
      display: button.style.display,
      visibility: button.style.visibility,
      position: button.style.position,
      zIndex: button.style.zIndex
    });

    // Test button click
    console.log('🧪 Testing button click...');
    button.click();

    setTimeout(() => {
      const panel = document.getElementById('tiptop-panel');
      if (panel && panel.style.display !== 'none') {
        console.log('✅ Button click test PASSED - Panel opened');
      } else {
        console.log('❌ Button click test FAILED - Panel not opened');
      }
    }, 500);

  } else {
    console.log('❌ Button NOT found in DOM');

    // Try to create button manually
    console.log('🧪 Attempting to create button manually...');
    if (window.createTipTopButton) {
      window.createTipTopButton();
      setTimeout(() => {
        const newButton = document.getElementById('tiptop-button');
        if (newButton) {
          console.log('✅ Button created successfully');
        } else {
          console.log('❌ Button creation failed');
        }
      }, 100);
    }
  }
}

// Function to test social client functionality
function testSocialClient() {
  console.log('🧪 Testing social client...');

  if (window.TipTopSocial) {
    console.log('✅ TipTopSocial object found');
    console.log('🧪 Available methods:', Object.keys(window.TipTopSocial));

    if (window.TipTopSocial.initialize) {
      console.log('✅ Initialize method available');
    } else {
      console.log('❌ Initialize method missing');
    }
  } else {
    console.log('❌ TipTopSocial object not found');
  }
}

// Function to test navigation behavior
function testNavigationBehavior() {
  console.log('🧪 Testing navigation behavior...');

  // Check if chat log exists
  const chatLog = document.getElementById('tiptop-chat-log');
  if (chatLog) {
    console.log('🧪 Chat log found, current message count:', chatLog.children.length);

    // Add some test messages to simulate existing chat
    if (chatLog.children.length === 0) {
      chatLog.innerHTML = `
        <div class="chat-message">Test message 1</div>
        <div class="chat-message">Test message 2</div>
      `;
      console.log('🧪 Added test messages to chat log');
    }

    // Test 1: Simulate navigation by calling content script function directly
    console.log('🧪 Test 1: Calling resetSocialStateForNavigation directly...');
    if (window.resetSocialStateForNavigation) {
      window.resetSocialStateForNavigation({
        oldUrl: 'https://example.com/old',
        newUrl: window.location.href
      });

      setTimeout(() => {
        console.log('🧪 After direct reset - Chat log message count:', chatLog.children.length);
        if (chatLog.children.length === 0) {
          console.log('✅ Direct reset test PASSED');
        } else {
          console.log('❌ Direct reset test FAILED - UI not cleared');
        }
      }, 200);
    }

    // Test 2: Simulate navigation by sending background message
    setTimeout(() => {
      console.log('🧪 Test 2: Sending TIPTOP_RESET_UI message to background...');

      // Re-add test messages
      chatLog.innerHTML = `
        <div class="chat-message">Test message 3</div>
        <div class="chat-message">Test message 4</div>
      `;

      chrome.runtime.sendMessage({
        type: 'TIPTOP_RESET_UI',
        data: {
          oldUrl: 'https://example.com/old',
          newUrl: window.location.href
        }
      }).then(response => {
        console.log('🧪 Background response:', response);

        setTimeout(() => {
          console.log('🧪 After background reset - Chat log message count:', chatLog.children.length);
          if (chatLog.children.length === 0) {
            console.log('✅ Background reset test PASSED');
          } else {
            console.log('❌ Background reset test FAILED - UI not cleared');
          }
        }, 500);
      }).catch(error => {
        console.log('❌ Background reset test FAILED - Error:', error);
      });
    }, 1000);

  } else {
    console.log('❌ Chat log element not found');
  }
}

// Function to test WebSocket connection on panel open
function testWebSocketConnection() {
  console.log('🧪 Testing WebSocket connection on panel open...');
  
  // Check if TipTop social client exists
  if (window.TipTopSocial) {
    console.log('🧪 TipTop social client found');
    
    // Check connection status
    if (window.TipTopSocial.isWebSocketConnected && window.TipTopSocial.isWebSocketConnected()) {
      console.log('✅ WebSocket connection test PASSED - Already connected');
    } else {
      console.log('🧪 WebSocket not connected, testing loadMessagesWhenReady...');
      
      // Test the loadMessagesWhenReady function
      if (window.TipTopSocial.loadMessagesWhenReady) {
        window.TipTopSocial.loadMessagesWhenReady().then(() => {
          console.log('🧪 loadMessagesWhenReady completed');
          
          // Check connection again
          if (window.TipTopSocial.isWebSocketConnected && window.TipTopSocial.isWebSocketConnected()) {
            console.log('✅ WebSocket connection test PASSED - Connected after loadMessagesWhenReady');
          } else {
            console.log('❌ WebSocket connection test FAILED - Still not connected');
          }
        }).catch(error => {
          console.log('❌ WebSocket connection test FAILED - Error:', error);
        });
      } else {
        console.log('❌ loadMessagesWhenReady function not found');
      }
    }
  } else {
    console.log('❌ TipTop social client not found');
  }
}

// Function to test storage persistence (messages should NOT be cleared)
async function testStoragePersistence() {
  console.log('🧪 Testing storage persistence...');

  try {
    // Get current storage state
    const beforeStorage = await chrome.storage.local.get(null);
    const beforeMessageCount = Object.keys(beforeStorage).filter(key => key.startsWith('tiptop_msg_')).length;
    console.log('🧪 Messages in storage before navigation:', beforeMessageCount);

    // Add a test message to storage
    const testMessage = {
      messageId: 'test_msg_' + Date.now(),
      url: window.location.href,
      content: 'Test message for persistence',
      timestamp: new Date().toISOString(),
      userId: 'test_user',
      userName: 'Test User',
      type: 'chat'
    };

    await chrome.storage.local.set({
      [`tiptop_msg_${testMessage.messageId}`]: testMessage
    });

    console.log('🧪 Added test message to storage');

    // Simulate navigation (should NOT clear storage)
    if (window.resetSocialStateForNavigation) {
      window.resetSocialStateForNavigation({
        oldUrl: window.location.href,
        newUrl: 'https://example.com/new-page'
      });
    }

    // Check storage after a delay - messages should still be there
    setTimeout(async () => {
      const afterStorage = await chrome.storage.local.get(null);
      const afterMessageCount = Object.keys(afterStorage).filter(key => key.startsWith('tiptop_msg_')).length;
      console.log('🧪 Messages in storage after navigation:', afterMessageCount);

      if (afterMessageCount >= beforeMessageCount + 1) {
        console.log('✅ Storage persistence test PASSED - Messages preserved');
      } else {
        console.log('❌ Storage persistence test FAILED - Messages were cleared');
      }
    }, 1000);

  } catch (error) {
    console.log('❌ Storage persistence test FAILED - Error:', error);
  }
}

// Run tests when TipTop panel is opened
function runTests() {
  console.log('🧪 Running TipTop navigation tests...');

  testNavigationBehavior();
  testWebSocketConnection();
  testStoragePersistence();
}

// Comprehensive test function
function runAllTests() {
  console.log('🧪 Running comprehensive TipTop tests...');

  // Test 1: Button functionality
  testButtonFunctionality();

  // Test 2: Social client (after delay)
  setTimeout(() => {
    testSocialClient();
  }, 1000);

  // Test 3: Navigation behavior (after delay)
  setTimeout(() => {
    testNavigationBehavior();
  }, 2000);

  // Test 4: WebSocket connection (after delay)
  setTimeout(() => {
    testWebSocketConnection();
  }, 3000);

  // Test 5: Storage persistence (after delay)
  setTimeout(() => {
    testStoragePersistence();
  }, 4000);
}

// Export test functions for manual testing
window.TipTopNavigationTests = {
  runTests,
  runAllTests,
  testButtonFunctionality,
  testSocialClient,
  testNavigationBehavior,
  testWebSocketConnection,
  testStoragePersistence
};

console.log('🧪 TipTop Navigation Test Script Ready');
console.log('🧪 Use TipTopNavigationTests.runAllTests() to run comprehensive tests');
console.log('🧪 Use TipTopNavigationTests.runTests() to run navigation-specific tests');
