// Test script for user list synchronization debugging
// Run this in the browser console to test user list sync

console.log('🧪 Starting user list synchronization test...');

// Test function to monitor user list updates
function testUserListSync() {
  console.log('🔍 Current user list state:');
  console.log('- activeUsers length:', window.TipTopSocial?.activeUsers?.length || 0);
  console.log('- activeUsers:', window.TipTopSocial?.activeUsers?.map(u => u.userName) || []);
  console.log('- isConnected:', window.TipTopSocial?.isConnected || false);
  console.log('- backgroundConnected:', window.TipTopSocial?.backgroundConnected || false);
  console.log('- socialEnabled:', window.TipTopSocial?.socialEnabled || false);
  
  // Check UI elements
  const userListElement = document.getElementById('tiptop-user-list');
  if (userListElement) {
    console.log('- UI user list children:', userListElement.children.length);
    const userElements = userListElement.querySelectorAll('[data-user-id]');
    console.log('- UI users:', Array.from(userElements).map(el => el.querySelector('.tiptop-user-name')?.textContent));
  } else {
    console.log('- UI user list element not found');
  }
}

// Test function to request user list manually
function requestUserList() {
  console.log('🔄 Manually requesting user list...');
  if (window.TipTopSocial?.sendWebSocketMessage) {
    window.TipTopSocial.sendWebSocketMessage({
      type: 'get_users',
      url: window.location.href
    });
    console.log('✅ User list request sent');
  } else {
    console.log('❌ sendWebSocketMessage not available');
  }
}

// Test function to simulate navigation
function simulateNavigation() {
  console.log('🔄 Simulating navigation...');
  if (window.resetSocialStateForNavigation) {
    window.resetSocialStateForNavigation({
      oldUrl: window.location.href,
      newUrl: window.location.href + '?test=1'
    });
    console.log('✅ Navigation simulation complete');
  } else {
    console.log('❌ resetSocialStateForNavigation not available');
  }
}

// Monitor user list updates
let updateCount = 0;
const originalHandleUsersUpdate = window.TipTopSocial?.handleUsersUpdate;
if (originalHandleUsersUpdate) {
  window.TipTopSocial.handleUsersUpdate = function(usersMessage) {
    updateCount++;
    console.log(`🔥 User list update #${updateCount}:`, {
      source: usersMessage.source,
      userCount: usersMessage.users?.length || 0,
      users: usersMessage.users?.map(u => u.userName) || []
    });
    return originalHandleUsersUpdate.call(this, usersMessage);
  };
  console.log('✅ User list update monitoring enabled');
}

// Export test functions to global scope
window.testUserListSync = testUserListSync;
window.requestUserList = requestUserList;
window.simulateNavigation = simulateNavigation;

console.log('🧪 Test functions available:');
console.log('- testUserListSync() - Check current state');
console.log('- requestUserList() - Manually request user list');
console.log('- simulateNavigation() - Simulate navigation reset');

// Run initial test
testUserListSync();

// Set up periodic monitoring
const monitorInterval = setInterval(() => {
  console.log('📊 Periodic user list check:');
  testUserListSync();
}, 10000); // Every 10 seconds

// Clean up function
window.stopUserListTest = function() {
  clearInterval(monitorInterval);
  console.log('🛑 User list test monitoring stopped');
};

console.log('🧪 User list synchronization test setup complete!');
console.log('📊 Monitoring will run every 10 seconds. Call stopUserListTest() to stop.');
