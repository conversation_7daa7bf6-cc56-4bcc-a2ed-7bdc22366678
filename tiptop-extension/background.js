// TipTop Background Service Worker (background.js)

// Default configuration
let TipTopConfig = {
  isTestMode: false,
  apiUrls: {
    tiptop: 'https://tiptop.qubitrhythm.com/tiptop',
    ask: 'https://tiptop.qubitrhythm.com/ask',
    test: 'https://tiptop.qubitrhythm.com/tiptop-test',
  },
  features: {
    useMockData: false,
  }
};

// Function to check for the mode indicator file
async function checkModeIndicatorFile() {
  try {
    // Try to fetch the mode indicator file
    const response = await fetch(chrome.runtime.getURL('tiptop-mode.json'));

    if (response.ok) {
      const modeData = await response.json();
      console.log('Found mode indicator file:', modeData);

      // Update the test mode based on the file
      const isTestMode = modeData.isTestMode === true || modeData.mode === 'localtest';
      const useMockData = modeData.useMockData === true;

      TipTopConfig = {
        isTestMode: isTestMode,
        apiUrls: {
          tiptop: isTestMode ? 'http://localhost:30080/tiptop' : 'https://tiptop.qubitrhythm.com/tiptop',
          ask: isTestMode ? 'http://localhost:30080/ask' : 'https://tiptop.qubitrhythm.com/ask',
          test: isTestMode ? 'http://localhost:30080/tiptop-test' : 'https://tiptop.qubitrhythm.com/tiptop-test',
        },
        features: {
          useMockData: useMockData,
        }
      };

      // Update chrome.storage to match
      chrome.storage.local.set({
        'TIPTOP_TEST_MODE': isTestMode ? 'true' : 'false',
        'TIPTOP_USE_MOCK_DATA': useMockData ? 'true' : 'false'
      });

      console.log(`Mode set to ${isTestMode ? 'LOCAL TEST' : 'PRODUCTION'} from indicator file`);
      console.log('TipTop Configuration loaded from mode indicator file:', TipTopConfig);
      console.log('Current API URLs:', getCurrentApiUrls());

      // Update the global USE_MOCK_DATA flag
      USE_MOCK_DATA = useMockData;

      return true;
    }
  } catch (error) {
    console.log('Mode indicator file not found or invalid, using storage setting');
  }

  return false;
}

// Load configuration from chrome.storage.local or mode indicator file
// Service workers in Manifest V3 don't have access to localStorage
async function loadConfiguration() {
  // First try to load from mode indicator file
  const fileFound = await checkModeIndicatorFile();

  if (!fileFound) {
    // Fall back to chrome.storage.local
    chrome.storage.local.get(['TIPTOP_TEST_MODE', 'TIPTOP_USE_MOCK_DATA'], function(result) {
      try {
        const isTestMode = result.TIPTOP_TEST_MODE === 'true';

        TipTopConfig = {
          isTestMode: isTestMode,
          apiUrls: {
            tiptop: isTestMode ? 'http://localhost:30080/tiptop' : 'https://tiptop.qubitrhythm.com/tiptop',
            ask: isTestMode ? 'http://localhost:30080/ask' : 'https://tiptop.qubitrhythm.com/ask',
            test: isTestMode ? 'http://localhost:30080/tiptop-test' : 'https://tiptop.qubitrhythm.com/tiptop-test',
          },
          features: {
            useMockData: result.TIPTOP_USE_MOCK_DATA === 'true',
          }
        };

        // Update the global USE_MOCK_DATA flag
        USE_MOCK_DATA = TipTopConfig.features.useMockData;

        // Log the configuration for debugging
        console.log('TipTop Configuration loaded from storage:', TipTopConfig);
        console.log('Current API URLs:', getCurrentApiUrls());
      } catch (e) {
        console.error('Error loading configuration from storage:', e);
      }
    });
  }
}

// Load configuration on startup
loadConfiguration();

// Function to get current API URLs based on configuration
function getCurrentApiUrls() {
  return {
    tiptop: TipTopConfig.apiUrls.tiptop,
    ask: TipTopConfig.apiUrls.ask,
    test: TipTopConfig.apiUrls.test
  };
}

// Log the initial configuration for debugging
console.log('TipTop Initial Configuration:', TipTopConfig);

// Flag to use mock data instead of real API calls - check configuration
let USE_MOCK_DATA = TipTopConfig.features.useMockData;

// Function to update mock data setting
function updateMockDataSetting() {
  // Service workers don't have access to window object, use TipTopConfig directly
  if (TipTopConfig && TipTopConfig.features) {
    USE_MOCK_DATA = TipTopConfig.features.useMockData === true;
    console.log('Mock data setting updated:', USE_MOCK_DATA);
  }
}

// Listen for storage changes to update mock data setting
chrome.storage.onChanged.addListener(function(changes, namespace) {
  if (namespace === 'local' && changes.TIPTOP_USE_MOCK_DATA) {
    const newValue = changes.TIPTOP_USE_MOCK_DATA.newValue === 'true';
    console.log('Mock data setting changed in storage:', newValue);

    // Update the global variable
    USE_MOCK_DATA = newValue;

    // Update the configuration directly
    if (TipTopConfig && TipTopConfig.features) {
      TipTopConfig.features.useMockData = newValue;
    }
  }
});

// NOTE: The server is currently using a self-signed certificate:
// Subject: O=Acme Co; CN=Kubernetes Ingress Controller Fake Certificate
// This is causing the "Failed to fetch" error in the extension.
// To fix this, you need to:
// 1. Obtain a proper SSL certificate for the domain (e.g., using Let's Encrypt)
// 2. Configure Kubernetes to use this certificate
// See the documentation for details on how to implement this.

// Function to generate mock AI responses for testing
function generateMockAIResponse(question, url) {
  console.log('Generating mock AI response for question:', question);
  console.log('URL context:', url);

  // Extract domain from URL for more realistic mock responses
  let domain = 'example.com';
  try {
    const urlObj = new URL(url);
    domain = urlObj.hostname;
  } catch (e) {
    console.error('Failed to parse URL:', e);
  }

  // Simple keyword matching for more relevant mock responses
  let answer = '';

  // Convert question to lowercase for easier matching
  const lowerQuestion = question.toLowerCase();

  if (lowerQuestion.includes('what') && lowerQuestion.includes('page')) {
    answer = `This page appears to be from ${domain}. It contains information that I've analyzed to provide you with a summary, key takeaways, and related resources. Is there something specific about the page content you'd like to know?`;
  } else if (lowerQuestion.includes('how') && lowerQuestion.includes('work')) {
    answer = `The TipTop extension works by analyzing the content of web pages you visit. It uses AI to generate summaries, extract key points, and suggest related resources. The extension also includes social features that let you connect with others viewing the same page.`;
  } else if (lowerQuestion.includes('who') || lowerQuestion.includes('author')) {
    answer = `I don't have specific information about the author of this page. The content appears to be from ${domain}, but I can't determine the individual author based on the available data.`;
  } else if (lowerQuestion.includes('when') || lowerQuestion.includes('date') || lowerQuestion.includes('published')) {
    answer = `I don't have information about when this specific page was published or last updated. This kind of metadata isn't always available for analysis.`;
  } else if (lowerQuestion.includes('why') || lowerQuestion.includes('purpose')) {
    answer = `Based on my analysis, this page from ${domain} appears to be designed to provide information about its topic. The specific purpose might be educational, promotional, or informational depending on the content.`;
  } else if (lowerQuestion.includes('where') || lowerQuestion.includes('location')) {
    answer = `The content is hosted on ${domain}, but I don't have specific information about physical locations mentioned in the content without doing a deeper analysis.`;
  } else if (lowerQuestion.includes('summary') || lowerQuestion.includes('summarize')) {
    answer = `I've already provided a summary of this page in the Summary section above. The summary highlights the main points and key information from the content. Is there a specific aspect you'd like me to elaborate on?`;
  } else if (lowerQuestion.includes('key') && lowerQuestion.includes('point')) {
    answer = `The key points from this page are listed in the Key Takeaways section above. These represent the most important information and concepts from the content. Is there a specific takeaway you'd like me to explain further?`;
  } else if (lowerQuestion.includes('resource') || lowerQuestion.includes('link')) {
    answer = `I've provided related resources in the Tips & Links section above. These are selected based on relevance to the page content. Some may be directly referenced in the content, while others are supplementary resources on related topics.`;
  } else {
    // Generic response for questions that don't match specific patterns
    answer = `Based on my analysis of this page from ${domain}, I can tell you that it contains information related to its main topic. The summary and key takeaways above highlight the most important points. If you have more specific questions about the content, feel free to ask.`;
  }

  return {
    question: question,
    answer: answer,
    timestamp: new Date().toISOString()
  };
}

// Function to generate mock data for testing
function generateMockData(url) {
  console.log('Generating mock data for URL:', url);

  // Extract domain from URL for more realistic mock data
  let domain = 'example.com';
  try {
    const urlObj = new URL(url);
    domain = urlObj.hostname;
  } catch (e) {
    console.error('Failed to parse URL:', e);
  }

  return {
    summary: {
      text: `This page appears to be about testing the TipTop UI interface. The content discusses UI improvements including background images, color schemes, and styling for different content blocks. The page also mentions the importance of professional design elements and visual hierarchy in creating an engaging user experience. The document explores how effective UI design can significantly impact user engagement and retention rates. It highlights several key principles of modern interface design, including consistency, clear visual hierarchy, and intuitive navigation patterns. The page further examines how color psychology plays a role in creating emotional connections with users, noting that blue conveys trust and professionalism while warmer colors like orange can create a sense of energy and enthusiasm. Typography choices are also discussed as critical elements that affect readability and overall user experience. Let's test some HTML entities: &quot;quotes&quot; and apostrophes like it&#039;s working properly.`,
      source: "TipTop AI Analysis"
    },
    // Add reading time estimate (more prominent)
    readingTime: {
      minutes: 4,
      words: 820
    },
    // Add key takeaways (more prominent)
    keyTakeaways: [
      "IMPORTANT: The TipTop UI has been redesigned with professional color schemes and background patterns",
      "FEATURE: Different content blocks now have distinct styling for better visual hierarchy",
      "IMPROVEMENT: HTML entities are properly decoded to improve text readability",
      "NEW: Reading time estimates and key takeaways have been added to enhance user experience"
    ],
    // Combined resources (replaces separate tips and links)
    resources: [
      {
        title: "UI Design Principles",
        description: "When designing interfaces, focus on consistency, clear visual hierarchy, and intuitive navigation to improve user experience.",
        url: "https://www.nngroup.com/articles/ten-usability-heuristics/"
      },
      {
        title: "Color Psychology &amp; Design",
        description: "Different colors evoke different emotions. Blue conveys trust and professionalism, which is why it&#039;s commonly used in business applications. &quot;Color theory&quot; is an essential concept in UI design.",
        url: "https://www.colorpsychology.org/"
      },
      {
        title: "Visual Hierarchy",
        description: "Use size, color, contrast, and spacing to guide users' attention to the most important elements first.",
        url: "https://www.interaction-design.org/literature/topics/visual-hierarchy"
      },
      {
        title: "Related: Material Design Guidelines",
        description: "External resource related to UI design.",
        url: "https://material.io/design"
      },
      {
        title: "Related: Web Content Accessibility Guidelines (WCAG &quot;2.1&quot;)",
        description: "External resource related to accessibility.",
        url: "https://www.w3.org/WAI/standards-guidelines/wcag/"
      },
      {
        title: "Related: Chrome Extension Development",
        description: "External resource related to chrome extension.",
        url: "https://developer.chrome.com/docs/extensions/"
      }
    ],
    keywords: ["UI design", "user interface", "color scheme", "typography", "visual hierarchy", "accessibility", "chrome extension"],
    // Add affiliate resources
    affiliateResources: [
      {
        title: "Adobe Creative Cloud",
        description: "Professional design tools for UI/UX designers. Create stunning interfaces with industry-standard software.",
        price: "$52.99/month",
        url: "https://www.adobe.com/creativecloud.html?sdid=TTGWPD68&mv=search&ef_id=123456:G:s&s_kwcid=AL!3085!10!79027267747116!79027969756135",
        imageUrl: "https://www.adobe.com/content/dam/shared/images/product-icons/svg/creative-cloud.svg"
      },
      {
        title: "Figma Professional",
        description: "Collaborative interface design tool. Create, test, and iterate on designs in real-time with your team.",
        price: "$12/editor/month",
        url: "https://www.figma.com/pricing/?utm_source=tiptop&utm_medium=affiliate&utm_campaign=tiptop_extension",
        imageUrl: "https://cdn.sanity.io/images/599r6htc/localized/46a76c802176eb17b04e12108de7e7e0f3736dc6-1024x1024.png?w=670&h=670&q=75&fit=max&auto=format"
      },
      {
        title: "UI Design Course Bundle",
        description: "Learn professional UI design from scratch. 40+ hours of video tutorials and practical exercises.",
        price: "$89.99",
        url: "https://www.udemy.com/course/ui-design-bundle/?utm_source=tiptop&utm_medium=affiliate&utm_campaign=tiptop_extension",
        imageUrl: "https://img-c.udemycdn.com/course/480x270/1452908_8741_4.jpg"
      },
      {
        title: "The Design of Everyday Things",
        description: "Essential book on design principles by Don Norman. Learn how to create intuitive and user-friendly interfaces.",
        price: "$15.99",
        url: "https://www.amazon.com/Design-Everyday-Things-Revised-Expanded/dp/0465050654/?tag=tiptopextension-20",
        imageUrl: "https://m.media-amazon.com/images/I/410RTQezHYL._SY445_SX342_.jpg"
      }
    ],
    responseTime: 123, // Mock response time in ms
    isMockData: true // Flag to indicate this is mock data
  };
}

console.log('TipTop Background Service Worker started.');

// ===== WEBSOCKET MANAGEMENT IN BACKGROUND =====

// WebSocket connection state
let backgroundSocket = null;
let isConnected = false;
let reconnectAttempts = 0;
let reconnectTimeout = null;
let heartbeatInterval = null;
let idleTimeout = null; // For connection idle management
const MAX_RECONNECT_ATTEMPTS = 10;
const INITIAL_RECONNECT_DELAY = 1000; // 1 second
const MAX_RECONNECT_DELAY = 30000; // 30 seconds
const HEARTBEAT_INTERVAL = 15000; // 15 seconds (reduced for faster detection)
const HEARTBEAT_TIMEOUT = 10000; // 10 seconds timeout for pong response
const WS_IDLE_TIMEOUT = 5 * 60 * 1000; // 5 minutes idle timeout

// Store the current connection info for reconnection
let currentConnectionInfo = null;

// Store active tabs and their URLs for message routing
const activeTabs = new Map(); // tabId -> { url, userId, userName }

// WebSocket configuration
const wsConfig = {
  urls: [
    'wss://ws.tiptop.qubitrhythm.com',
    'ws://localhost:8080'
  ]
};

// Initialize WebSocket connection for a specific URL and user
async function initializeWebSocketConnection(url, userId, userName) {
  console.log('🚀 initializeWebSocketConnection called with:', { url, userId, userName });
  console.log('🔍 Current connection state:', {
    hasSocket: !!backgroundSocket,
    isConnected,
    readyState: backgroundSocket ? backgroundSocket.readyState : 'no socket'
  });

  // Store user info for this connection
  const connectionInfo = { url, userId, userName };

  // If we already have a connection, check if it's for the same URL
  if (backgroundSocket && isConnected) {
    // Get current URL from the socket (if available) or check if we need to switch
    const currentSocketUrl = backgroundSocket.url;
    const newSocketUrl = `${wsConfig.urls[0]}?url=${encodeURIComponent(url)}&userId=${userId}&userName=${encodeURIComponent(userName)}`;

    // If the URL matches, just send presence update
    if (currentSocketUrl && currentSocketUrl.includes(encodeURIComponent(url))) {
      console.log('✅ WebSocket already connected for this URL, sending presence update');
      sendWebSocketMessage({
        type: 'presence',
        userId,
        userName,
        url
      });
      return true;
    } else {
      console.log('🔄 WebSocket connected but for different URL, need to reconnect');
      // Continue with new connection establishment
    }
  }

  // Connect to WebSocket
  console.log('🔗 No existing connection, calling connectWebSocket...');
  const result = await connectWebSocket(connectionInfo);
  console.log('🔗 connectWebSocket result:', result);
  return result;
}

// Connect to WebSocket server
async function connectWebSocket(connectionInfo) {
  console.log('🔗 connectWebSocket called with:', connectionInfo);

  if (backgroundSocket && backgroundSocket.readyState === WebSocket.OPEN) {
    console.log('✅ WebSocket already connected');
    return true;
  }

  const { url, userId, userName } = connectionInfo;
  const encodedUrl = encodeURIComponent(url);

  console.log('🔍 WebSocket config URLs:', wsConfig.urls);

  // Try each WebSocket URL
  for (let i = 0; i < wsConfig.urls.length; i++) {
    const wsUrl = `${wsConfig.urls[i]}?url=${encodedUrl}&userId=${userId}&userName=${encodeURIComponent(userName)}`;

    try {
      console.log(`🚀 Attempting WebSocket connection ${i + 1}/${wsConfig.urls.length} to: ${wsUrl}`);

      const socket = new WebSocket(wsUrl);

      // Set up connection promise
      const connectionPromise = new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Connection timeout'));
        }, 5000);

        socket.onopen = () => {
          clearTimeout(timeout);
  console.log('WebSocket connected successfully');
  backgroundSocket = socket;
  isConnected = true;
  reconnectAttempts = 0;
  
  // Start idle timer on new connection
  resetIdleTimer();

          // Set up message handlers
          setupWebSocketHandlers(socket);

          // Send presence message to register user in room
          sendWebSocketMessage({
            type: 'presence',
            userId: userId,
            userName: userName,
            url: url
          });

          // Request message history
          requestMessageHistory(url);

          // Request user list
          setTimeout(() => {
            sendWebSocketMessage({
              type: 'get_users',
              url: url
            });
          }, 500);

          // Replay any buffered messages after connection is established
          setTimeout(() => {
            replayBufferedMessages();
          }, 1000);

          // Test message to content script
          setTimeout(() => {
            console.log('🧪 Sending test message to content script');
            broadcastToActiveTabs({
              type: 'TIPTOP_TEST_MESSAGE',
              data: { message: 'Hello from background!' }
            });
          }, 1500);

          resolve(true);
        };

        socket.onerror = (error) => {
          clearTimeout(timeout);
          console.error('WebSocket connection error:', error);
          reject(error);
        };

        socket.onclose = () => {
          clearTimeout(timeout);
          reject(new Error('Connection closed'));
        };
      });

      await connectionPromise;
      return true;

    } catch (error) {
      console.error(`Failed to connect to ${wsConfig.urls[i]}:`, error);
      if (i === wsConfig.urls.length - 1) {
        // All attempts failed
        scheduleReconnect(connectionInfo);
        return false;
      }
    }
  }

  return false;
}

// Set up WebSocket event handlers
function setupWebSocketHandlers(socket) {
  // Set up heartbeat
  socket.isAlive = true;

  socket.onmessage = (event) => {
    try {
      const message = JSON.parse(event.data);
      console.log('Background WebSocket received message:', message);

      // Handle different message types
      handleWebSocketMessage(message);

    } catch (error) {
      console.error('Error parsing WebSocket message:', error);
    }
  };

    socket.onclose = (event) => {
      console.log('WebSocket connection closed:', event.code, event.reason);
      isConnected = false;
      backgroundSocket = null;

      // Clear heartbeat interval
      if (heartbeatInterval) {
        clearInterval(heartbeatInterval);
        heartbeatInterval = null;
      }

      // Attempt to reconnect if not intentionally closed
      if (event.code !== 1000) {
        // If we have current connection info, use it for reconnection
        if (currentConnectionInfo) {
          scheduleReconnect(currentConnectionInfo);
        } else {
          // Try to get connection info from active tabs
          if (activeTabs.size > 0) {
            const firstTab = activeTabs.values().next().value;
            scheduleReconnect(firstTab);
          }
        }
      }
    };

  socket.onerror = (error) => {
    console.error('WebSocket error:', error);
  };

  socket.onopen = () => {
    console.log('WebSocket connection opened');
    socket.isAlive = true;
    isConnected = true;
    reconnectAttempts = 0; // Reset reconnect attempts on successful connection

    // Start heartbeat
    startHeartbeat(socket);
  };

  // Handle pong responses
  socket.addEventListener('pong', () => {
    console.log('Received pong from server');
    socket.isAlive = true;
  });
}

// Handle incoming WebSocket messages
function handleWebSocketMessage(message) {
  // Reset idle timer for all users when any message is received
  resetIdleTimer();
  
  switch (message.type) {
    case 'chat':
    case 'note':
      // Store message and broadcast to all tabs
      storeAndBroadcastMessage(message);
      break;

    case 'users':
      // Broadcast user list to all tabs
      broadcastToAllTabs({
        type: 'TIPTOP_USERS_UPDATE',
        data: message
      });
      break;

    case 'history':
      // Handle message history
      handleMessageHistory(message);
      break;

    case 'scroll_chat':
      // Handle scroll commands - broadcast to all tabs
      broadcastToAllTabs({
        type: 'TIPTOP_SCROLL_CHAT',
        data: message
      });
      break;

    case 'users':
      // Handle user list updates
      broadcastToAllTabs({
        type: 'TIPTOP_USERS_UPDATE',
        data: message
      });
      break;

    default:
      console.log('Unknown WebSocket message type:', message.type);
  }
}

// Message buffer for reliable delivery during reconnections
let messageBuffer = [];
const MAX_BUFFER_SIZE = 50;

// Store message and broadcast to all tabs
async function storeAndBroadcastMessage(message) {
  // Store in chrome.storage.local for persistence
  const messageId = message.messageId || `msg_${Date.now()}_${Math.random()}`;
  const storageKey = `tiptop_msg_${messageId}`;

  try {
    // Store individual message
    await chrome.storage.local.set({
      [storageKey]: {
        ...message,
        messageId,
        receivedAt: Date.now()
      }
    });

    // Update last received message ID
    await chrome.storage.local.set({
      'tiptop_last_received_message_id': messageId
    });

    console.log('Message stored with ID:', messageId);

    // Add to message buffer for reliable delivery
    messageBuffer.push({
      type: 'TIPTOP_CHAT_MESSAGE',
      data: message,
      timestamp: Date.now()
    });

    // Keep buffer size manageable
    if (messageBuffer.length > MAX_BUFFER_SIZE) {
      messageBuffer = messageBuffer.slice(-MAX_BUFFER_SIZE);
    }

    // Broadcast to active tabs with retry mechanism
    broadcastMessageWithRetry({
      type: 'TIPTOP_CHAT_MESSAGE',
      data: message
    });

  } catch (error) {
    console.error('Error storing message:', error);
  }
}

// Broadcast message with retry mechanism for reliability
function broadcastMessageWithRetry(message, retryCount = 0) {
  const maxRetries = 3;
  const retryDelay = 1000; // 1 second

  console.log(`Broadcasting message (attempt ${retryCount + 1}):`, message.type);

  // Try to broadcast to active tabs
  let successCount = 0;
  let totalTabs = activeTabs.size;

  if (totalTabs === 0) {
    console.log('No active tabs to broadcast to');
    return;
  }

  let completedTabs = 0;

  activeTabs.forEach((tabInfo, tabId) => {
    chrome.tabs.sendMessage(tabId, message, (response) => {
      completedTabs++;

      if (chrome.runtime.lastError) {
        console.log(`Tab ${tabId} cannot receive messages:`, chrome.runtime.lastError.message);
      } else {
        successCount++;
        console.log(`Message sent to active tab ${tabId}`);
      }

      // Check if we've tried all tabs
      if (completedTabs === totalTabs) {
        // If no tabs received the message and we haven't exceeded retries
        if (successCount === 0 && retryCount < maxRetries) {
          console.log(`No tabs received message, retrying in ${retryDelay}ms (attempt ${retryCount + 1}/${maxRetries})`);
          setTimeout(() => {
            broadcastMessageWithRetry(message, retryCount + 1);
          }, retryDelay);
        } else if (successCount > 0) {
          console.log(`Message successfully delivered to ${successCount}/${totalTabs} tabs`);
        } else {
          console.warn(`Failed to deliver message after ${maxRetries} attempts`);
        }
      }
    });
  });
}

// Replay buffered messages after WebSocket reconnection
function replayBufferedMessages() {
  if (messageBuffer.length === 0) {
    console.log('No buffered messages to replay');
    return;
  }

  console.log(`Replaying ${messageBuffer.length} buffered messages`);

  // Filter out old messages (older than 30 seconds)
  const now = Date.now();
  const recentMessages = messageBuffer.filter(msg => (now - msg.timestamp) < 30000);

  if (recentMessages.length < messageBuffer.length) {
    console.log(`Filtered out ${messageBuffer.length - recentMessages.length} old messages`);
    messageBuffer = recentMessages;
  }

  // Replay recent messages
  recentMessages.forEach((message, index) => {
    setTimeout(() => {
      console.log(`Replaying buffered message ${index + 1}/${recentMessages.length}:`, message.type);
      broadcastMessageWithRetry(message);
    }, index * 100); // Stagger replays by 100ms
  });
}

// Broadcast message to active tabs only (legacy function for compatibility)
function broadcastToActiveTabs(message) {
  broadcastMessageWithRetry(message);
}

// Request message history from server
function requestMessageHistory(url) {
  if (!backgroundSocket || !isConnected) {
    console.log('Cannot request history: WebSocket not connected');
    return;
  }

  // Get last received message ID from storage
  chrome.storage.local.get(['tiptop_last_received_message_id'], (result) => {
    const lastMessageId = result.tiptop_last_received_message_id;

    console.log('Requesting message history, lastMessageId:', lastMessageId);

    sendWebSocketMessage({
      type: 'get_history',
      url: url,
      lastMessageId: lastMessageId || null
    });
  });
}

// Send message through WebSocket
function sendWebSocketMessage(message) {
  if (!backgroundSocket || !isConnected) {
    console.error('Cannot send message: WebSocket not connected');
    return false;
  }

  try {
    backgroundSocket.send(JSON.stringify(message));
    return true;
  } catch (error) {
    console.error('Error sending WebSocket message:', error);
    return false;
  }
}

// Start heartbeat mechanism
function startHeartbeat(socket) {
  if (heartbeatInterval) {
    clearInterval(heartbeatInterval);
  }

  heartbeatInterval = setInterval(() => {
    if (socket.isAlive === false) {
      console.log('No pong response received, closing connection');
      socket.close(1000, 'Heartbeat timeout');
      return;
    }

    // Mark as potentially dead and send ping
    socket.isAlive = false;

    try {
      // Send ping and set timeout for pong response
      socket.ping();
      console.log('Sent ping to server');
      
      // Set a timeout to check for pong response
      setTimeout(() => {
        if (socket.isAlive === false) {
          console.log('Pong timeout, closing connection');
          socket.close(1000, 'Pong timeout');
        }
      }, HEARTBEAT_TIMEOUT);
      
    } catch (error) {
      console.error('Error sending ping:', error);
      socket.close(1000, 'Ping error');
    }
  }, HEARTBEAT_INTERVAL);

  console.log('Heartbeat started');
}

// Schedule reconnection with exponential backoff
function scheduleReconnect(connectionInfo) {
  if (reconnectTimeout) {
    clearTimeout(reconnectTimeout);
  }

  if (reconnectAttempts >= MAX_RECONNECT_ATTEMPTS) {
    console.error('Max reconnection attempts reached');
    // Reset attempts after a longer period to allow server recovery
    setTimeout(() => {
      reconnectAttempts = 0;
      console.log('Reconnection attempts reset');
    }, 60000); // 1 minute
    return;
  }

  const delay = Math.min(
    INITIAL_RECONNECT_DELAY * Math.pow(2, reconnectAttempts),
    MAX_RECONNECT_DELAY
  );

  console.log(`Scheduling reconnection attempt ${reconnectAttempts + 1} in ${delay}ms`);

  reconnectTimeout = setTimeout(() => {
    reconnectAttempts++;
    
    // Store connection info for future reconnections
    currentConnectionInfo = connectionInfo;
    
    console.log('Attempting to reconnect with info:', connectionInfo);
    connectWebSocket(connectionInfo);
  }, delay);
}

// Handle message history from server
async function handleMessageHistory(historyMessage) {
  console.log('Handling message history:', historyMessage);

  if (!historyMessage.messages || !Array.isArray(historyMessage.messages)) {
    console.log('No messages in history response');
    return;
  }

  // DO NOT clear existing messages - just store new ones
  // Messages should persist across navigation for other users
  console.log(`Processing ${historyMessage.messages.length} history messages`);

  // Store each message (storeAndBroadcastMessage handles deduplication)
  for (const message of historyMessage.messages) {
    await storeAndBroadcastMessage(message);
  }

  // Broadcast history loaded event
  broadcastToAllTabs({
    type: 'TIPTOP_HISTORY_LOADED',
    data: {
      messageCount: historyMessage.messages.length,
      url: historyMessage.url
    }
  });
}

async function clearStoredMessages() {
  const keys = await chrome.storage.local.get(null);
  for (const key in keys) {
    if (key.startsWith('tiptop_msg_')) {
      await chrome.storage.local.remove(key);
    }
  }
  console.log('Cleared stored messages');
}

// Note: We no longer clear stored messages for specific URLs
// Messages should persist on the server for all users viewing that URL
// Only the local UI should be cleared when users navigate

// Broadcast message to all tabs
function broadcastToAllTabs(message) {
  chrome.tabs.query({}, (tabs) => {
    tabs.forEach(tab => {
      chrome.tabs.sendMessage(tab.id, message, (response) => {
        if (chrome.runtime.lastError) {
          // Silently ignore tabs that can't receive messages
          console.log(`Tab ${tab.id} cannot receive messages:`, chrome.runtime.lastError.message);
        }
      });
    });
  });
}

// Function to reset idle timer on user activity
function resetIdleTimer() {
  // Clear any existing timeout
  if (idleTimeout) {
    clearTimeout(idleTimeout);
    idleTimeout = null;
  }
  
  // Set new timeout to disconnect after idle period
  idleTimeout = setTimeout(() => {
    if (backgroundSocket && isConnected) {
      console.log('WebSocket idle timeout reached. Disconnecting.');
      disconnectWebSocket();
    }
  }, WS_IDLE_TIMEOUT);
}

// Function to send messages via storage when WebSocket fails
function sendMessageViaStorage(messageData, tabId) {
  console.log('Using storage fallback for message:', messageData);
  
  // Create message object with required fields
  const message = {
    type: 'chat',
    content: messageData.content,
    userId: messageData.userId,
    userName: messageData.userName,
    timestamp: new Date().toISOString(),
    messageId: `storage_${Date.now()}_${Math.floor(Math.random() * 10000)}`,
    url: messageData.url
  };
  
  // Store and broadcast the message
  storeAndBroadcastMessage(message);
  
  // Trigger reconnection for all active users
  if (activeTabs.size > 0) {
    activeTabs.forEach((tabInfo, tabId) => {
      console.log(`Triggering reconnect for tab ${tabId}`);
      initializeWebSocketConnection(tabInfo.url, tabInfo.userId, tabInfo.userName);
    });
  }
  
  return true;
}

// Function to cleanly disconnect WebSocket
function disconnectWebSocket() {
  if (backgroundSocket) {
    console.log('Closing WebSocket connection');
    backgroundSocket.close(1000, 'Idle timeout');
    backgroundSocket = null;
    isConnected = false;
    
    // Clear heartbeat interval
    if (heartbeatInterval) {
      clearInterval(heartbeatInterval);
      heartbeatInterval = null;
    }
  }
}

// Clean up when tabs are closed
chrome.tabs.onRemoved.addListener((tabId) => {
  if (activeTabs.has(tabId)) {
    console.log('Cleaning up closed tab:', tabId);
    activeTabs.delete(tabId);

    // If no more active tabs, schedule WebSocket closure with delay
    // This prevents premature closure during page navigation/refresh
    if (activeTabs.size === 0 && backgroundSocket) {
      console.log('No active tabs remaining, scheduling WebSocket closure in 30 seconds');
      setTimeout(() => {
        // Double-check that we still have no active tabs
        if (activeTabs.size === 0 && backgroundSocket) {
          console.log('Closing WebSocket after delay - no active tabs');
          backgroundSocket.close(1000, 'No active tabs');
          backgroundSocket = null;
          isConnected = false;
        } else {
          console.log('WebSocket closure cancelled - tabs became active again');
        }
      }, 30000); // 30 second delay
    }
  }
});

// Listen for messages from content scripts or potentially popup/action
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log('🔥 Background received message:', message.type, 'from tab:', sender.tab?.id, 'at:', new Date().toISOString());
  console.log('🔍 Message details:', message);
  console.log('🔍 Sender details:', { tabId: sender.tab?.id, url: sender.tab?.url, frameId: sender.frameId });

  // Handle tab ID requests from content scripts
  if (message.type === 'GET_TAB_ID') {
    console.log('📋 Returning tab ID:', sender.tab?.id);
    sendResponse({ tabId: sender.tab?.id });
    return true;
  }

  // Handle WebSocket connection requests from content scripts
  if (message.type === 'TIPTOP_INIT_WEBSOCKET') {
    console.log('🚀 Initializing WebSocket connection for tab:', sender.tab?.id);
    console.log('🔍 Connection request data:', message.data);
    console.log('🔍 Current WebSocket state:', {
      hasSocket: !!backgroundSocket,
      isConnected,
      readyState: backgroundSocket ? backgroundSocket.readyState : 'no socket'
    });

    const { url, userId, userName } = message.data;

    // Store tab info
    if (sender.tab?.id) {
      activeTabs.set(sender.tab.id, { url, userId, userName });
      console.log('📋 Stored tab info for tab:', sender.tab.id);
    }

    // Initialize WebSocket connection and reset idle timer
    console.log('🔗 Calling initializeWebSocketConnection...');
    initializeWebSocketConnection(url, userId, userName)
      .then(success => {
        console.log('✅ WebSocket initialization result:', success, 'connected:', isConnected);
        resetIdleTimer(); // Reset timer on new connection
        sendResponse({ success, connected: isConnected });
      })
      .catch(error => {
        console.error('❌ Failed to initialize WebSocket:', error);
        sendResponse({ success: false, error: error.message });
      });

    return true; // Indicate async response
  }

  // Handle chat message sending
    if (message.type === 'TIPTOP_SEND_CHAT_MESSAGE') {
        console.log('🚀 Processing chat message send request');
        resetIdleTimer(); // Reset idle timer on user activity
        console.log('📤 Message data:', message.data);
        console.log('🔌 WebSocket state:', {
          hasSocket: !!backgroundSocket,
          isConnected,
          readyState: backgroundSocket ? backgroundSocket.readyState : 'no socket',
          activeTabs: activeTabs.size
        });

        try {
          const { data } = message;
          
          // Ensure this tab is in activeTabs
          if (sender.tab?.id) {
            activeTabs.set(sender.tab.id, {
              url: data.url,
              userId: data.userId,
              userName: data.userName
            });
          }

          // Check WebSocket connection status first
          if (!backgroundSocket || !isConnected) {
            console.log('❌ WebSocket not connected when trying to send message');
            console.log('🔍 Connection details:', {
              hasSocket: !!backgroundSocket,
              isConnected,
              readyState: backgroundSocket ? backgroundSocket.readyState : 'no socket',
              activeTabs: activeTabs.size,
              activeTabsList: Array.from(activeTabs.keys())
            });

            // If we have active tabs, attempt to reconnect
            if (activeTabs.size > 0) {
              console.log('🔄 Attempting to reconnect WebSocket for active tabs');
              // Get the first active tab's info to reconnect
              const firstTabId = Array.from(activeTabs.keys())[0];
              const tabInfo = activeTabs.get(firstTabId);
              
              if (tabInfo) {
                return initializeWebSocketConnection(tabInfo.url, tabInfo.userId, tabInfo.userName)
                  .then(success => {
                    if (success) {
                      console.log('✅ WebSocket reconnected, retrying message send');
                      // Retry the message send
                      const retrySuccess = sendWebSocketMessage({
                        type: 'chat',
                        ...data
                      });
                      
                      // Send response to content script
                      sendResponse({ success: retrySuccess, timestamp: new Date().toISOString() });
                      return true;
                    }
                    
                    // If reconnection failed, use storage fallback
                    console.log('⚠️ WebSocket reconnection failed, using storage fallback');
                    const storageSuccess = sendMessageViaStorage(data, sender.tab?.id);
                    sendResponse({ 
                      success: storageSuccess, 
                      timestamp: new Date().toISOString(),
                      message: storageSuccess ? 'Message queued' : 'Storage fallback failed'
                    });
                    return true;
                  })
                  .catch(error => {
                    console.error('❌ WebSocket reconnection error:', error);
                    // Use storage fallback on error
                    const storageSuccess = sendMessageViaStorage(data, sender.tab?.id);
                    sendResponse({ 
                      success: storageSuccess, 
                      error: 'Connection failed, message queued',
                      timestamp: new Date().toISOString()
                    });
                    return true;
                  });
              }
            }

            // If no active tabs, use storage fallback directly
            const storageSuccess = sendMessageViaStorage(data, sender.tab?.id);
            sendResponse({ 
              success: storageSuccess, 
              timestamp: new Date().toISOString(),
              message: storageSuccess ? 'Message queued' : 'Storage fallback failed'
            });
            return true;
          }

          // If WebSocket is connected, send immediately
          const success = sendWebSocketMessage({
            type: 'chat',
            ...data
          });

          console.log('✅ Chat message send result:', success);
          sendResponse({ success, timestamp: new Date().toISOString() });
          return true;
        } catch (error) {
          console.error('❌ Error sending chat message:', error);
          sendResponse({ 
            success: false, 
            error: error.message, 
            timestamp: new Date().toISOString(),
            message: 'Failed to send message'
          });
          return true;
        }
      }

  // Handle WebSocket disconnection
  if (message.type === 'TIPTOP_DISCONNECT_WEBSOCKET') {
    console.log('Disconnecting WebSocket for tab:', sender.tab?.id);

    if (sender.tab?.id) {
      activeTabs.delete(sender.tab.id);
    }

    // If no more active tabs, schedule WebSocket closure with delay
    if (activeTabs.size === 0 && backgroundSocket) {
      console.log('No active tabs remaining after disconnect, scheduling WebSocket closure in 30 seconds');
      setTimeout(() => {
        // Double-check that we still have no active tabs
        if (activeTabs.size === 0 && backgroundSocket) {
          console.log('Closing WebSocket after disconnect delay - no active tabs');
          backgroundSocket.close(1000, 'No active tabs');
          backgroundSocket = null;
          isConnected = false;
        } else {
          console.log('WebSocket closure cancelled after disconnect - tabs became active again');
        }
      }, 30000); // 30 second delay
    }

    sendResponse({ success: true });
    return true;
  }

  // Handle message history requests
  if (message.type === 'TIPTOP_REQUEST_HISTORY') {
    console.log('Requesting message history for:', message.data.url);

    requestMessageHistory(message.data.url);
    sendResponse({ success: true });
    return true;
  }

  // Handle generic WebSocket message sending
  if (message.type === 'TIPTOP_SEND_WEBSOCKET_MESSAGE') {
    console.log('Sending WebSocket message through background:', message.data);

    const success = sendWebSocketMessage(message.data);
    sendResponse({ success });
    return true;
  }

  // Handle UI reset for navigation
  if (message.type === 'TIPTOP_RESET_UI') {
    console.log('🔄 Background received TIPTOP_RESET_UI for tab:', sender.tab?.id, message.data);

    // Update tab info if needed
    if (sender.tab?.id && message.data?.newUrl) {
      const tabInfo = activeTabs.get(sender.tab.id);
      if (tabInfo) {
        tabInfo.url = message.data.newUrl;
        activeTabs.set(sender.tab.id, tabInfo);
        console.log('Updated tab URL in activeTabs:', message.data.newUrl);
      }
    }

    sendResponse({ success: true });
    return true;
  }

  // Handle chat messages and relay them to other tabs (legacy support)
  if (message.type === 'TIPTOP_CHAT_MESSAGE') {
    console.log('Relaying chat message to other tabs:', message);

    // Store the message in sync storage for cross-browser communication
    if (message.data && message.data.url && message.data.messageId) {
      const currentUrl = message.data.url;
      const urlKey = btoa(currentUrl).replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');
      const messagesKey = `tiptop_msgs_${urlKey}`;

      // Create the message object
      const messageObj = {
        type: 'chat',
        content: message.data.content,
        userId: message.data.userId,
        userName: message.data.userName,
        timestamp: message.data.timestamp,
        messageId: message.data.messageId,
        url: currentUrl,
        storageTimestamp: Date.now(),
        relayedByBackground: true // Flag to indicate this was relayed by background
      };

      // First get existing messages
      chrome.storage.sync.get([messagesKey], function(result) {
        let urlMessages = result[messagesKey] || [];

        // Check if this message already exists
        const existingIndex = urlMessages.findIndex(m => m.messageId === messageObj.messageId);
        if (existingIndex >= 0) {
          console.log('Message already exists in sync storage, not adding again');
        } else {
          // Add the new message
          urlMessages.push(messageObj);

          // Limit to 20 messages
          if (urlMessages.length > 20) {
            urlMessages = urlMessages.slice(-20);
          }

          // Save back to storage
          const storageData = {};
          storageData[messagesKey] = urlMessages;

          chrome.storage.sync.set(storageData, function() {
            if (chrome.runtime.lastError) {
              console.error('Error saving to sync storage:', chrome.runtime.lastError);
              // Fall back to local storage
              chrome.storage.local.set(storageData);
            }

            // Also save a timestamp entry
            const storageKey = `tiptop_ts_${Date.now()}`;
            const timestampData = {};
            timestampData[storageKey] = {
              messageId: messageObj.messageId,
              urlKey: urlKey
            };
            chrome.storage.sync.set(timestampData);
          });
        }
      });
    }

    // Get all tabs
    chrome.tabs.query({}, (tabs) => {
      // Send the message to all tabs except the sender
      tabs.forEach(tab => {
        // Skip the sender tab
        if (sender.tab && tab.id === sender.tab.id) {
          return;
        }

        // Send the message to this tab
        try {
          chrome.tabs.sendMessage(tab.id, message);
        } catch (e) {
          console.error('Error sending message to tab:', tab.id, e);
        }
      });
    });

    // Send a response to acknowledge receipt
    sendResponse({ relayed: true });
    return true; // Indicate we'll send a response asynchronously
  }

  if (message.type === 'GET_TIPTOP_DATA') {
    // Get the current active tab in the current window
    chrome.tabs.query({ active: true, currentWindow: true }, async (tabs) => {
      if (chrome.runtime.lastError) {
        console.error("Error querying tabs:", chrome.runtime.lastError);
        sendResponse({ success: false, error: "Could not get current tab information." });
        return;
      }
      if (!tabs || tabs.length === 0 || !tabs[0].url) {
        console.error("No active tab found or tab has no URL.");
        sendResponse({ success: false, error: "Could not get URL from current tab." });
        return;
      }

      const currentUrl = tabs[0].url;
      console.log(`Fetching TipTop data for URL: ${currentUrl}`);

      // If mock data flag is set, return mock data instead of making API calls
      if (USE_MOCK_DATA) {
        console.log('Using mock data instead of real API call');
        const mockData = generateMockData(currentUrl);
        console.log('Generated mock data:', mockData);
        sendResponse({ success: true, data: mockData });
        return;
      }

      // --- Fetch with Timeout ---
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout - increased to give server more time

      try {
        // Get current API URLs
        const apiUrls = getCurrentApiUrls();

        // Log the request details for debugging
        console.log('Making API request to:', apiUrls.tiptop);
        console.log('Request body:', JSON.stringify({ url: currentUrl }));

        // Use the current API URL
        const response = await fetch(apiUrls.tiptop, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          // Simplify the request body to just include the URL
          body: JSON.stringify({ url: currentUrl }),
          credentials: 'omit', // Keep omit unless cookies/auth needed cross-origin
          signal: controller.signal // Link fetch to the AbortController
        });

        clearTimeout(timeoutId); // Clear timeout if fetch completes in time

        if (!response.ok) {
          // Handle HTTP errors (e.g., 404, 500)
          console.error(`API request failed with status: ${response.status}`);
          let errorText = `API Error: ${response.statusText} (Status: ${response.status})`;
          try {
            // Try to get more specific error from response body
            const errorData = await response.json();
            console.log('Error response body:', errorData);
            errorText = errorData.error || errorData.message || errorText;
          } catch (e) {
            console.error('Failed to parse error response as JSON:', e);
            try {
              // Try to get the response as text
              const textResponse = await response.text();
              console.log('Error response as text:', textResponse);
              errorText += ` - Response: ${textResponse.substring(0, 100)}...`;
            } catch (textError) {
              console.error('Failed to get response as text:', textError);
            }
          }
          sendResponse({ success: false, error: errorText });
          return; // Don't proceed further
        }

        const data = await response.json();
        console.log("API Response Data:", data);
        sendResponse({ success: true, data: data });

      } catch (error) {
        clearTimeout(timeoutId); // Clear timeout if fetch fails

        // Log detailed error information
        console.error('API request failed:', error);
        console.error('Error details:', {
          name: error.name,
          message: error.message,
          stack: error.stack
        });

        // Categorize the error for better user feedback
        let errorMessage = error.message || 'Failed to fetch data from API.';

        if (errorMessage.includes('timeout') || error.name === 'AbortError') {
          errorMessage = 'Request timed out. The server took too long to respond.';
        } else if (errorMessage.includes('Network request failed')) {
          errorMessage = 'Network error. Please check your internet connection and try again. If the problem persists, the server might be down or unreachable.';
        } else if (errorMessage.includes('SSL') || errorMessage.includes('certificate') || errorMessage.includes('secure')) {
          errorMessage = 'SSL Certificate Error: The server has an invalid security certificate. This is likely a configuration issue with the server.';
        }

        // Fall back to mock data instead of showing an error
        console.log('API call failed, falling back to mock data');
        const mockData = generateMockData(currentUrl);
        // Add a flag to indicate this is fallback mock data
        mockData.isFallbackMockData = true;
        mockData.apiErrorMessage = errorMessage;
        console.log('Generated fallback mock data:', mockData);
        sendResponse({ success: true, data: mockData });
      }
    });

    // Return true to indicate you wish to send a response asynchronously
    return true;
  }

  // Handle AI question requests
  if (message.type === 'ASK_AI_QUESTION') {
    console.log('Received AI question:', message.data.question);

    // Get the current active tab in the current window
    chrome.tabs.query({ active: true, currentWindow: true }, async (tabs) => {
      if (chrome.runtime.lastError) {
        console.error("Error querying tabs:", chrome.runtime.lastError);
        sendResponse({ success: false, error: "Could not get current tab information." });
        return;
      }

      if (!tabs || tabs.length === 0 || !tabs[0].url) {
        console.error("No active tab found or tab has no URL.");
        sendResponse({ success: false, error: "Could not get URL from current tab." });
        return;
      }

      const currentUrl = tabs[0].url;
      const question = message.data.question;

      console.log(`Processing AI question for URL: ${currentUrl}`);
      console.log(`Question: ${question}`);

      // If mock data flag is set, return mock data instead of making API calls
      if (USE_MOCK_DATA) {
        console.log('Using mock data for AI question');
        const mockResponse = generateMockAIResponse(question, currentUrl);
        console.log('Generated mock AI response:', mockResponse);
        sendResponse({ success: true, data: mockResponse });
        return;
      }

      // Make a real API call to the AI service
      try {
        // Get current API URLs
        const apiUrls = getCurrentApiUrls();

        console.log('Making AI API request to:', apiUrls.ask);
        console.log('Request body:', JSON.stringify({ url: currentUrl, question: question }));

        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

        // Try the direct /ask endpoint first
        let response;
        try {
          response = await fetch(apiUrls.ask, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            url: currentUrl,
            question: question
          }),
          credentials: 'omit',
          signal: controller.signal
        });

          clearTimeout(timeoutId);

          if (!response.ok) {
            throw new Error(`API Error: ${response.statusText} (Status: ${response.status})`);
          }
        } catch (firstError) {
          console.error(`First API request failed:`, firstError);
          console.log('Trying fallback URL:', apiUrls.tiptop + '/ask');

          // Try the /tiptop/ask endpoint as fallback
          try {
            response = await fetch(apiUrls.tiptop + '/ask', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                url: currentUrl,
                question: question
              }),
              credentials: 'omit',
              signal: controller.signal
            });

            if (!response.ok) {
              throw new Error(`API Error: ${response.statusText} (Status: ${response.status})`);
            }
          } catch (secondError) {
            console.error(`Both API requests failed:`, secondError);
            let errorMessage = secondError.message || 'Unknown error occurred';

            // Return the error message instead of falling back to mock data
            sendResponse({
              success: false,
              error: errorMessage,
              data: {
                question: question,
                answer: `I'm sorry, I couldn't process your question. Error: ${errorMessage}`,
                timestamp: new Date().toISOString()
              }
            });
            return;
          }
        }

        clearTimeout(timeoutId);

        const data = await response.json();
        console.log("AI API Response Data:", data);

        // Format the response in the expected structure
        const aiResponse = {
          question: question,
          answer: data.answer || data.response || "Sorry, I couldn't generate a response for that question.",
          timestamp: new Date().toISOString()
        };

        sendResponse({ success: true, data: aiResponse });
      } catch (error) {
        console.error('AI API request failed:', error);

        // Return the error message instead of falling back to mock data
        const errorMessage = error.message || 'Unknown error occurred';
        sendResponse({
          success: false,
          error: errorMessage,
          data: {
            question: question,
            answer: `I'm sorry, I couldn't process your question. Error: ${errorMessage}`,
            timestamp: new Date().toISOString()
          }
        });
      }
    });

    // Return true to indicate you wish to send a response asynchronously
    return true;
  }

  // Handle test icon request
  if (message.type === 'TEST_ICONS') {
    console.log('Received request to test icons');

    // Execute the test-icons.js script in the active tab
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      if (tabs.length > 0) {
        chrome.scripting.executeScript({
          target: {tabId: tabs[0].id},
          files: ['test-icons.js']
        });
        sendResponse({success: true, message: 'Test script executed'});
      } else {
        sendResponse({success: false, error: 'No active tab found'});
      }
    });

    return true; // Indicate we'll send a response asynchronously
  }

  // Handle other message types if needed in the future
  // return false; // If not sending an async response for other types
});

// Example: Listener for browser action click (if you add an icon click action)
/*
chrome.action.onClicked.addListener((tab) => {
  console.log('TipTop action clicked for tab:', tab.id);
  // Send a message to the content script to trigger the UI or data fetch
  chrome.tabs.sendMessage(tab.id, { type: 'TOGGLE_TIPTOP_UI' });
});
*/

console.log('TipTop Background Service Worker listeners added.');

// Listen for storage changes to relay messages between browser instances
chrome.storage.onChanged.addListener(function(changes, namespace) {
  console.log(`Background script detected storage changes in ${namespace} storage:`, changes);

  // Handle storage-based message requests (fallback communication)
  for (const [key, change] of Object.entries(changes)) {
    if (key.startsWith('tiptop_send_request_') && change.newValue) {
      console.log('🔄 Processing storage-based message request:', key);
      handleStorageMessageRequest(key, change.newValue);
    }
  }

  // Check if we have a timestamp entry change (which indicates a new message)
  let timestampChanged = false;
  let messageId = null;
  let urlKey = null;

  Object.keys(changes).forEach(key => {
    // Check for old timestamp format
    if (key.startsWith('tiptop_msg_timestamp_')) {
      timestampChanged = true;
      messageId = changes[key].newValue;
    }

    // Check for new timestamp format
    if (key.startsWith('tiptop_ts_')) {
      timestampChanged = true;
      if (changes[key].newValue) {
        messageId = changes[key].newValue.messageId;
        urlKey = changes[key].newValue.urlKey;
      }
    }

    // Check for direct message changes
    if (key.startsWith('tiptop_msgs_')) {
      // Extract the URL key from the storage key
      const storageUrlKey = key.replace('tiptop_msgs_', '');

      // If we have new messages, check them
      if (changes[key].newValue && Array.isArray(changes[key].newValue)) {
        // Get the most recent message
        const messages = changes[key].newValue;
        if (messages.length > 0) {
          // Sort by timestamp (newest first)
          const sortedMessages = [...messages].sort((a, b) => {
            return new Date(b.timestamp) - new Date(a.timestamp);
          });

          // Get the newest message
          const latestMessage = sortedMessages[0];

          // Only relay if it's a chat message
          if (latestMessage.type === 'chat') {
            console.log('Found new chat message to relay:', latestMessage);
            relayMessageToAllTabs(latestMessage);
          }
        }
      }
    }
  });

  // If a timestamp changed, check for messages
  if (timestampChanged && messageId) {
    console.log('Background script detected storage timestamp change, messageId:', messageId);

    // If we have the URL key, we can directly get the messages for that URL
    if (urlKey) {
      const messagesKey = `tiptop_msgs_${urlKey}`;

      // Try sync storage first
      chrome.storage.sync.get([messagesKey], function(result) {
        if (chrome.runtime.lastError || !result[messagesKey]) {
          // Fall back to local storage
          chrome.storage.local.get([messagesKey], function(localResult) {
            if (!chrome.runtime.lastError && localResult[messagesKey]) {
              findAndRelayMessage(localResult[messagesKey], messageId);
            }
          });
          return;
        }

        findAndRelayMessage(result[messagesKey], messageId);
      });
    }
    // If we don't have the URL key, we need to check all messages
    else {
      // Get all storage keys
      chrome.storage.sync.get(null, function(result) {
        if (chrome.runtime.lastError) {
          // Fall back to local storage
          chrome.storage.local.get(null, function(localResult) {
            if (!chrome.runtime.lastError) {
              findMessageInAllStorage(localResult, messageId);
            }
          });
          return;
        }

        findMessageInAllStorage(result, messageId);
      });
    }
  }

  // Function to find a message in all storage
  function findMessageInAllStorage(storage, targetMessageId) {
    let foundMessage = null;

    Object.keys(storage).forEach(key => {
      // Only check message storage keys
      if (key.startsWith('tiptop_msgs_') && Array.isArray(storage[key])) {
        const messages = storage[key];
        const message = messages.find(m => m.messageId === targetMessageId);
        if (message) {
          foundMessage = message;
        }
      }
    });

    if (foundMessage) {
      console.log('Found message to relay:', foundMessage);
      relayMessageToAllTabs(foundMessage);
    }
  }

  // Function to find and relay a specific message
  function findAndRelayMessage(messages, targetMessageId) {
    if (!Array.isArray(messages)) return;

    const message = messages.find(m => m.messageId === targetMessageId);
    if (message) {
      console.log('Found message to relay:', message);
      relayMessageToAllTabs(message);
    }
  }

  // Function to relay a message to all tabs
  function relayMessageToAllTabs(message) {
    chrome.tabs.query({}, (tabs) => {
      tabs.forEach(tab => {
        try {
          chrome.tabs.sendMessage(tab.id, {
            type: 'TIPTOP_CHAT_MESSAGE',
            data: message
          });
        } catch (e) {
          console.error('Error sending message to tab:', tab.id, e);
        }
      });
    });
  }
});

// Handle storage-based message requests (fallback communication)
async function handleStorageMessageRequest(requestKey, requestData) {
  console.log('🔄 Handling storage message request:', requestKey, requestData);

  try {
    const responseKey = requestKey.replace('_request_', '_response_');
    let success = false;

    if (requestData.type === 'SEND_CHAT_MESSAGE') {
      // Check WebSocket connection status first
      if (!backgroundSocket || !isConnected) {
        console.error('❌ Storage handler: WebSocket not connected');
        console.error('🔍 Storage handler connection details:', {
          hasSocket: !!backgroundSocket,
          isConnected,
          readyState: backgroundSocket ? backgroundSocket.readyState : 'no socket'
        });
        success = false;
      } else {
        // Process the chat message
        success = sendWebSocketMessage({
          type: 'chat',
          ...requestData.data
        });

        console.log('📤 Storage-based chat message send result:', success);
      }
    }

    // Store the response
    await chrome.storage.local.set({
      [responseKey]: {
        success: success,
        timestamp: Date.now(),
        requestKey: requestKey
      }
    });

    console.log('📥 Storage response stored:', responseKey);

    // Clean up the request after a delay
    setTimeout(async () => {
      try {
        await chrome.storage.local.remove(requestKey);
        console.log('🧹 Cleaned up storage request:', requestKey);
      } catch (error) {
        console.error('Error cleaning up storage request:', error);
      }
    }, 10000); // Clean up after 10 seconds

  } catch (error) {
    console.error('❌ Error handling storage message request:', error);

    // Store error response
    const responseKey = requestKey.replace('_request_', '_response_');
    try {
      await chrome.storage.local.set({
        [responseKey]: {
          success: false,
          error: error.message,
          timestamp: Date.now()
        }
      });
    } catch (storageError) {
      console.error('Failed to store error response:', storageError);
    }
  }
}

// Add web navigation listener to detect URL changes and reset state
chrome.webNavigation.onCommitted.addListener(async details => {
  if (details.frameId === 0) { // Only process main frame navigation
    const newUrl = details.url;
    const tabId = details.tabId;

    // Check if the tab is in activeTabs
    if (activeTabs.has(tabId)) {
      const tabInfo = activeTabs.get(tabId);
      const oldUrl = tabInfo.url;

      if (oldUrl !== newUrl) {
        console.log(`🔄 Navigation detected: ${oldUrl} -> ${newUrl} for tab ${tabId}`);

        // Send reset message to content script to clear local UI
        chrome.tabs.sendMessage(tabId, {
          type: 'TIPTOP_RESET_UI',
          data: { oldUrl, newUrl }
        }).catch(error => {
          // Ignore errors if content script is not ready yet
          console.log('Could not send reset message to content script:', error.message);
        });

        // Update the tab's URL in activeTabs
        tabInfo.url = newUrl;
        activeTabs.set(tabId, tabInfo);
        console.log('Updated tab info for navigation:', tabId, newUrl);

        // CRITICAL FIX: Reconnect WebSocket with new URL parameters
        // This ensures each page gets its own WebSocket context
        if (backgroundSocket && isConnected) {
          console.log('🔄 Reconnecting WebSocket for new URL:', newUrl);

          // Store the old socket reference
          const oldSocket = backgroundSocket;

          // Immediately start establishing new connection (don't wait)
          console.log('🔗 Establishing new WebSocket connection for URL:', newUrl);
          initializeWebSocketConnection(newUrl, tabInfo.userId, tabInfo.userName)
            .then(success => {
              if (success) {
                console.log('✅ WebSocket reconnected for new URL');
                // Only close old socket after new one is established
                if (oldSocket && oldSocket.readyState === WebSocket.OPEN) {
                  console.log('🔌 Closing old WebSocket connection');
                  oldSocket.close();
                }
              } else {
                console.error('❌ Failed to reconnect WebSocket for new URL');
                // Keep old socket if new connection failed
                backgroundSocket = oldSocket;
                isConnected = true;
              }
            })
            .catch(error => {
              console.error('❌ Error during WebSocket reconnection:', error);
              // Keep old socket if new connection failed
              backgroundSocket = oldSocket;
              isConnected = true;
            });
        }
      }
    } else {
      console.log(`No active tab info for tab ${tabId} during navigation`);
    }
  }
});

console.log('TipTop Background Service Worker listeners added.');
