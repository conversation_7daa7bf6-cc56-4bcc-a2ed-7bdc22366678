// TipTop Content Script (content.js)

console.log("TipTop Content Script Loaded.");

// Add CSS for animations and styling
const style = document.createElement('style');
style.textContent = `
    /* Social Features Styling */
    #tiptop-collaboration {
        margin-top: 20px;
        border-top: 1px solid rgba(0, 0, 0, 0.1);
        padding-top: 15px;
    }

    .tiptop-collaboration-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
    }

    .tiptop-collaboration-title {
        font-weight: 600;
        color: #2c3e50;
        display: flex;
        align-items: center;
    }

    .tiptop-collaboration-title svg {
        margin-right: 6px;
        color: #3498db;
    }

    .tiptop-collaboration-toggle {
        position: relative;
        display: inline-block;
        width: 40px;
        height: 20px;
    }

    .tiptop-collaboration-toggle input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    .tiptop-toggle-slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        transition: .4s;
        border-radius: 34px;
    }

    .tiptop-toggle-slider:before {
        position: absolute;
        content: "";
        height: 16px;
        width: 16px;
        left: 2px;
        bottom: 2px;
        background-color: white;
        transition: .4s;
        border-radius: 50%;
    }

    input:checked + .tiptop-toggle-slider {
        background-color: #3498db;
    }

    input:checked + .tiptop-toggle-slider:before {
        transform: translateX(20px);
    }

    .tiptop-collaboration-status {
        font-size: 12px;
        color: #7f8c8d;
        margin-bottom: 10px;
    }

    .tiptop-status-connected {
        color: #2ecc71;
    }

    .tiptop-status-connecting {
        color: #f39c12;
    }

    .tiptop-status-disconnected {
        color: #e74c3c;
    }

    .tiptop-collaboration-controls {
        margin-top: 10px;
    }

    /* Social loading state */
    .tiptop-collaboration-loading {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 30px 20px;
        background-color: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #e0e6ed;
        margin-top: 10px;
    }

    .tiptop-loading-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 15px;
    }

    .tiptop-loading-spinner {
        width: 24px;
        height: 24px;
        border: 3px solid #e0e6ed;
        border-top: 3px solid #3498db;
        border-radius: 50%;
        animation: tiptop-spin 1s linear infinite;
    }

    .tiptop-loading-text {
        color: #7f8c8d;
        font-size: 14px;
        font-weight: 500;
    }

    .tiptop-loading-dots {
        animation: tiptop-loading-dots 1.5s infinite;
    }

    @keyframes tiptop-spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    @keyframes tiptop-loading-dots {
        0%, 20% { content: ''; }
        40% { content: '.'; }
        60% { content: '..'; }
        80%, 100% { content: '...'; }
    }

    .tiptop-tabs {
        display: flex;
        border-bottom: 1px solid #e0e6ed;
        margin-bottom: 15px;
    }

    .tiptop-tab-button {
        padding: 8px 12px;
        background: none;
        border: none;
        border-bottom: 2px solid transparent;
        cursor: pointer;
        font-size: 13px;
        color: #7f8c8d;
        transition: all 0.2s ease;
    }

    .tiptop-tab-button.active {
        color: #3498db;
        border-bottom-color: #3498db;
        font-weight: 600;
    }

    .tiptop-tab-button:hover:not(.active) {
        color: #2c3e50;
        background-color: #f8f9fa;
    }

    .tiptop-tab-content {
        display: none;
    }

    .tiptop-tab-content.active {
        display: block;
    }

    .tiptop-user-list {
        display: flex;
        flex-direction: column;
        gap: 8px;
        margin-bottom: 15px;
        max-height: 120px;
        overflow-y: auto;
        padding-right: 5px;
        scrollbar-width: thin;
    }

    /* Webkit scrollbar styling */
    .tiptop-user-list::-webkit-scrollbar {
        width: 6px;
    }

    .tiptop-user-list::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 10px;
    }

    .tiptop-user-list::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 10px;
    }

    .tiptop-user-list::-webkit-scrollbar-thumb:hover {
        background: #a1a1a1;
    }

    .tiptop-user {
        display: flex;
        align-items: center;
        background-color: #f1f8fe;
        border-radius: 12px;
        padding: 6px 8px;
        font-size: 12px;
        width: 100%;
        box-sizing: border-box;
    }

    .tiptop-user.faded {
        opacity: 0.5;
    }

    .tiptop-user-avatar {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        background-color: #3498db;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 10px;
        margin-right: 6px;
        font-weight: bold;
        position: relative;
    }

    .tiptop-user-status {
        position: absolute;
        bottom: -2px;
        right: -2px;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        border: 2px solid white;
    }

    .tiptop-status-online {
        background-color: #2ecc71;
    }

    .tiptop-status-offline {
        background-color: #95a5a6;
    }

    .tiptop-status-away {
        background-color: #f39c12;
    }

    .tiptop-status-busy {
        background-color: #e74c3c;
    }

    /* User name inline editing styles */
    .tiptop-user-name {
        font-size: 13px;
        color: #2c3e50;
        font-weight: 500;
        cursor: pointer;
        transition: color 0.2s ease;
        padding: 2px 4px;
        border-radius: 4px;
        min-width: 60px;
        display: inline-block;
    }

    .tiptop-user-name:hover {
        color: #3498db;
        background-color: #f8f9fa;
    }

    .tiptop-user-name.editing {
        background-color: white;
        border: 2px solid #3498db;
        color: #2c3e50;
        outline: none;
        cursor: text;
    }

    .tiptop-no-users {
        font-style: italic;
        color: #95a5a6;
        font-size: 12px;
        padding: 10px;
        text-align: center;
        background-color: #f8f9fa;
        border-radius: 6px;
        border: 1px dashed #e0e6ed;
    }

    .tiptop-chat-container {
        height: 150px;
        overflow-y: auto;
        overflow-x: hidden;
        border: 1px solid #e0e6ed;
        border-radius: 8px;
        background-color: #f8f9fa;
        margin-bottom: 10px;
        padding: 10px;
        position: relative; /* For absolute positioning of new message indicator */
        scroll-behavior: smooth; /* Enable smooth scrolling */
        -webkit-overflow-scrolling: touch; /* Improve scrolling on iOS */
    }

    .tiptop-chat-log {
        display: flex;
        flex-direction: column;
        gap: 12px;
        min-height: 100%;
        width: 100%;
        overflow-anchor: auto; /* Modern browsers: keep scroll position at bottom when content changes */
    }

    .tiptop-chat-message {
        display: flex;
        gap: 8px;
        margin-bottom: 8px;
        position: relative;
        max-width: 100%;
    }

    .tiptop-message-avatar {
        width: 28px;
        height: 28px;
        border-radius: 50%;
        background-color: #3498db;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 10px;
        font-weight: bold;
        flex-shrink: 0;
    }

    .tiptop-message-bubble {
        padding: 8px 10px;
        border-radius: 8px;
        background-color: #e8f4fd;
        position: relative;
        max-width: calc(100% - 36px);
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    .tiptop-own-message .tiptop-message-bubble {
        background-color: #edf7ed;
    }

    .tiptop-own-message {
        flex-direction: row-reverse;
    }

    .tiptop-message-sender {
        font-weight: bold;
        font-size: 11px;
        margin-bottom: 3px;
        color: #2980b9;
    }

    .tiptop-own-message .tiptop-message-sender {
        color: #27ae60;
    }

    .tiptop-message-content {
        font-size: 13px;
        line-height: 1.4;
        word-break: break-word;
    }

    .tiptop-message-content.truncated {
        max-height: 3.6em; /* Approximately 3 lines */
        overflow: hidden;
        position: relative;
    }

    .tiptop-message-content.truncated::after {
        content: '';
        position: absolute;
        bottom: 0;
        right: 0;
        width: 30px;
        height: 1.2em;
        background: linear-gradient(to right, transparent, #e8f4fd 70%);
    }

    .tiptop-own-message .tiptop-message-content.truncated::after {
        background: linear-gradient(to right, transparent, #edf7ed 70%);
    }

    .tiptop-show-more {
        color: #3498db;
        cursor: pointer;
        font-size: 12px;
        text-decoration: underline;
        margin-top: 4px;
        display: inline-block;
    }

    .tiptop-show-more:hover {
        color: #2980b9;
    }

    .tiptop-message-time {
        font-size: 10px;
        color: #95a5a6;
        text-align: right;
        margin-top: 3px;
    }

    .tiptop-chat-input {
        display: flex;
        gap: 8px;
    }

    .tiptop-chat-input input {
        flex: 1;
        padding: 8px 10px;
        border: 1px solid #e0e6ed;
        border-radius: 6px;
        font-size: 13px;
    }

    .tiptop-chat-input button {
        background-color: #3498db;
        color: white;
        border: none;
        border-radius: 6px;
        padding: 0 12px;
        font-size: 13px;
        cursor: pointer;
        transition: background-color 0.2s ease;
    }

    .tiptop-chat-input button:hover {
        background-color: #2980b9;
    }

    .tiptop-notification {
        padding: 8px 12px;
        border-radius: 6px;
        margin-bottom: 10px;
        font-size: 13px;
        display: none;
    }

    .tiptop-notification-info {
        background-color: #e8f4fd;
        border: 1px solid #c1d9f0;
        color: #2980b9;
    }

    .tiptop-notification-error {
        background-color: #ffeaec;
        border: 1px solid #f5c6cb;
        color: #dc3545;
    }

    .tiptop-notification-success {
        background-color: #edf7ed;
        border: 1px solid #c8e6c9;
        color: #27ae60;
    }

    .tiptop-settings-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
        padding: 8px 0;
        border-bottom: 1px solid #f1f1f1;
    }

    .tiptop-settings-label {
        font-size: 13px;
        color: #2c3e50;
    }

    .tiptop-name-input {
        display: flex;
        gap: 8px;
        margin-top: 10px;
    }

    .tiptop-name-input input {
        flex: 1;
        padding: 8px 10px;
        border: 1px solid #e0e6ed;
        border-radius: 6px;
        font-size: 13px;
    }

    .tiptop-name-input button {
        background-color: #3498db;
        color: white;
        border: none;
        border-radius: 6px;
        padding: 0 12px;
        font-size: 13px;
        cursor: pointer;
    }

    /* Notes section styling */
    .tiptop-notes-container {
        height: 150px;
        overflow-y: auto;
        border: 1px solid #e0e6ed;
        border-radius: 8px;
        background-color: #f8f9fa;
        margin-bottom: 10px;
        padding: 10px;
    }

    .tiptop-no-notes {
        font-style: italic;
        color: #95a5a6;
        font-size: 12px;
        padding: 10px;
        text-align: center;
        background-color: #f8f9fa;
        border-radius: 6px;
        border: 1px dashed #e0e6ed;
    }

    .tiptop-notes-list {
        display: flex;
        flex-direction: column;
        gap: 12px;
    }

    .tiptop-note {
        background-color: #fff9e6;
        border: 1px solid #ffe8a1;
        border-radius: 8px;
        padding: 10px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .tiptop-note-header {
        display: flex;
        justify-content: space-between;
        margin-bottom: 5px;
        font-size: 11px;
    }

    .tiptop-note-author {
        font-weight: bold;
        color: #f39c12;
    }

    .tiptop-note-time {
        color: #95a5a6;
    }

    .tiptop-note-text {
        font-size: 13px;
        line-height: 1.4;
        color: #2c3e50;
    }

    .tiptop-note-input {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .tiptop-note-input textarea {
        width: 100%;
        height: 80px;
        padding: 8px 10px;
        border: 1px solid #e0e6ed;
        border-radius: 6px;
        font-size: 13px;
        resize: vertical;
        font-family: inherit;
    }

    .tiptop-note-input button {
        background-color: #f39c12;
        color: white;
        border: none;
        border-radius: 6px;
        padding: 8px 12px;
        font-size: 13px;
        cursor: pointer;
        align-self: flex-end;
        transition: background-color 0.2s ease;
    }

    .tiptop-note-input button:hover {
        background-color: #e67e22;
    }

    /* AI Interaction Styles */
    .tiptop-ai-interaction {
        display: flex;
        flex-direction: column;
        gap: 15px;
    }

    .tiptop-ai-question-input {
        display: flex;
        gap: 8px;
    }

    .tiptop-ai-question-input input {
        flex: 1;
        padding: 8px 10px;
        border: 1px solid #e0e6ed;
        border-radius: 6px;
        font-size: 13px;
    }

    .tiptop-ai-question-input button {
        background-color: #3498db;
        color: white;
        border: none;
        border-radius: 6px;
        padding: 0 12px;
        font-size: 13px;
        cursor: pointer;
        transition: background-color 0.2s ease;
    }

    .tiptop-ai-question-input button:hover {
        background-color: #2980b9;
    }

    .tiptop-ai-response-container {
        background-color: #f8f9fa;
        border: 1px solid #e0e6ed;
        border-radius: 8px;
        padding: 12px;
        margin-top: 10px;
    }

    .tiptop-ai-response-header {
        margin-bottom: 8px;
        color: #2c3e50;
        font-size: 14px;
    }

    #tiptop-ai-response {
        font-size: 13px;
        line-height: 1.5;
        color: #2c3e50;
        margin: 0;
    }

    .tiptop-ai-loading {
        display: flex;
        align-items: center;
        gap: 10px;
        color: #7f8c8d;
        font-size: 13px;
        margin: 10px 0;
    }

    .tiptop-ai-loading .dot {
        width: 8px;
        height: 8px;
        background-color: #3498db;
        border-radius: 50%;
        display: inline-block;
        animation: tiptop-dot-pulse 1.4s infinite ease-in-out both;
    }

    .tiptop-ai-loading .dot:nth-child(1) {
        animation-delay: -0.32s;
    }

    .tiptop-ai-loading .dot:nth-child(2) {
        animation-delay: -0.16s;
    }

    /* Chat history styling */
    .tiptop-chat-history-toggle {
        margin: 10px 0;
        text-align: center;
    }

    .tiptop-history-button {
        background-color: #f8f9fa;
        border: 1px solid #e0e6ed;
        border-radius: 6px;
        padding: 6px 12px;
        font-size: 12px;
        color: #7f8c8d;
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        gap: 5px;
        transition: all 0.2s ease;
    }

    .tiptop-history-button:hover {
        background-color: #edf7ed;
        color: #27ae60;
    }

    .tiptop-chat-history {
        border: 1px solid #e0e6ed;
        border-radius: 8px;
        margin-bottom: 10px;
        background-color: #f8f9fa;
    }

    .tiptop-history-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 12px;
        border-bottom: 1px solid #e0e6ed;
    }

    .tiptop-history-header h4 {
        margin: 0;
        font-size: 14px;
        color: #2c3e50;
    }

    .tiptop-close-button {
        background: none;
        border: none;
        font-size: 18px;
        color: #95a5a6;
        cursor: pointer;
        padding: 0 5px;
    }

    .tiptop-close-button:hover {
        color: #e74c3c;
    }

    .tiptop-history-content {
        max-height: 200px;
        overflow-y: auto;
        padding: 10px;
    }

    .tiptop-no-history {
        font-style: italic;
        color: #95a5a6;
        font-size: 12px;
        text-align: center;
    }

    .tiptop-history-item {
        margin-bottom: 10px;
        padding: 8px;
        border-radius: 6px;
        border: 1px solid #e0e6ed;
        position: relative;
    }

    .tiptop-history-chat {
        background-color: #e8f4fd;
        border-color: #c1d9f0;
    }

    .tiptop-history-note {
        background-color: #fff9e6;
        border-color: #ffe8a1;
    }

    .tiptop-history-item-header {
        display: flex;
        justify-content: space-between;
        margin-bottom: 5px;
        font-size: 11px;
    }

    .tiptop-history-author {
        font-weight: bold;
    }

    .tiptop-chat-author {
        color: #2980b9;
    }

    .tiptop-note-author {
        color: #f39c12;
    }

    .tiptop-history-time {
        color: #95a5a6;
    }

    .tiptop-history-text {
        font-size: 13px;
        line-height: 1.4;
        color: #2c3e50;
    }

    .tiptop-history-chat::before {
        content: "💬";
        position: absolute;
        right: 5px;
        top: 5px;
        font-size: 10px;
        opacity: 0.5;
    }

    .tiptop-history-note::before {
        content: "📝";
        position: absolute;
        right: 5px;
        top: 5px;
        font-size: 10px;
        opacity: 0.5;
    }

    /* New message indicator styling */
    .tiptop-new-message-indicator {
        position: absolute;
        bottom: 10px;
        left: 50%;
        transform: translateX(-50%);
        background-color: #3498db;
        color: white;
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 12px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 5px;
        z-index: 10;
        animation: tiptop-bounce 1s infinite alternate;
    }

    .tiptop-new-message-indicator svg {
        transform: rotate(180deg);
    }

    @keyframes tiptop-bounce {
        from { transform: translateX(-50%) translateY(0); }
        to { transform: translateX(-50%) translateY(-5px); }
    }

    /* Animation keyframes */
    @keyframes tiptop-blink {
        0%, 100% { opacity: 1; }
        50% { opacity: 0; }
    }

    @keyframes tiptop-fade-in {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    @keyframes tiptop-dot-pulse {
        0%, 80%, 100% { transform: scale(0); }
        40% { transform: scale(1); }
    }

    /* Panel styling with enhanced floating effect */
    #tiptop-panel {
        background: #ffffff;
        background-image: linear-gradient(135deg, #f8f9fa 25%, #ffffff 25%, #ffffff 50%, #f8f9fa 50%, #f8f9fa 75%, #ffffff 75%, #ffffff 100%);
        background-size: 40px 40px;
        border: 1px solid rgba(225, 228, 232, 0.8);
        border-radius: 12px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.25), 0 5px 15px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.2) inset;
        font-family: 'Segoe UI', system-ui, -apple-system, BlinkMacSystemFont, sans-serif;
        backdrop-filter: blur(5px);
        transform: translateZ(0);
    }

    /* Header styling with enhanced shadow */
    #tiptop-panel h3 {
        color: #ffffff;
        font-size: 18px;
        font-weight: 600;
        margin: -18px -18px 15px -18px;
        padding: 15px;
        background-color: #3498db;
        background-image: linear-gradient(135deg, #3498db, #2980b9);
        border-radius: 12px 12px 0 0;
        text-align: center;
        letter-spacing: 0.5px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15), 0 1px 3px rgba(0, 0, 0, 0.1);
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    /* Section headers */
    #tiptop-panel h4 {
        color: #2c3e50;
        font-size: 16px;
        font-weight: 600;
        margin: 20px 0 10px 0;
        padding: 8px 12px;
        background-color: #f1f8fe;
        border-left: 4px solid #3498db;
        border-radius: 4px;
    }

    /* Content sections with enhanced shadow */
    .tiptop-content-section {
        transition: opacity 0.3s ease-in-out, transform 0.3s ease, box-shadow 0.3s ease;
        background-color: #ffffff;
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1), 0 2px 5px rgba(0, 0, 0, 0.05);
        border: 1px solid rgba(234, 236, 239, 0.8);
    }

    /* Only apply hover effects to sections with background and shadow */
    .tiptop-content-section:not([style*="background: transparent"]):not([style*="box-shadow: none"]):hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12), 0 3px 8px rgba(0, 0, 0, 0.06);
    }

    /* Summary section with improved styling and fixed height with scrolling */
    #tiptop-summary {
        line-height: 1.6;
        color: #2c3e50;
        font-size: 14px;
        background-color: #f0f7ff;
        background-image: linear-gradient(to bottom, #f8fbff, #f0f7ff);
        padding: 15px 18px;
        border-radius: 8px;
        margin: 0;
        border: 1px solid #d4e5f7;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05), 0 0 0 1px rgba(255, 255, 255, 0.7) inset;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        max-height: 200px; /* Fixed height for summary box */
        overflow-y: auto; /* Enable vertical scrolling */
    }

    #tiptop-summary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12), 0 3px 8px rgba(0, 0, 0, 0.06);
    }

    /* Custom scrollbar styling for the summary box */
    #tiptop-summary::-webkit-scrollbar {
        width: 8px;
    }

    #tiptop-summary::-webkit-scrollbar-track {
        background: rgba(0, 0, 0, 0.05);
        border-radius: 4px;
    }

    #tiptop-summary::-webkit-scrollbar-thumb {
        background: rgba(52, 152, 219, 0.5);
        border-radius: 4px;
    }

    #tiptop-summary::-webkit-scrollbar-thumb:hover {
        background: rgba(52, 152, 219, 0.7);
    }

    /* Reading Time section */
    .tiptop-reading-time {
        display: flex;
        align-items: center;
        margin: 0 0 15px 0;
        padding: 10px 15px;
        background-color: #e8f4fd;
        border-radius: 6px;
        border: 1px solid #c1d9f0;
        color: #2c3e50;
        font-size: 14px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .tiptop-reading-time svg {
        margin-right: 10px;
        color: #3498db;
        width: 18px;
        height: 18px;
        flex-shrink: 0;
    }

    .tiptop-reading-time strong {
        margin-right: 3px;
        color: #2c3e50;
    }

    /* Key Takeaways section */
    #tiptop-takeaways {
        list-style-type: none;
        padding-left: 0;
        margin: 0;
    }

    #tiptop-takeaways li {
        position: relative;
        margin-bottom: 10px;
        padding: 8px 8px 8px 28px;
        background-color: #edf7ed;
        border-radius: 6px;
        border: 1px solid #c8e6c9;
        color: #2e7d32;
    }

    #tiptop-takeaways li:last-child {
        margin-bottom: 0;
    }

    #tiptop-takeaways li:before {
        content: '✓';
        position: absolute;
        left: 10px;
        color: #2e7d32;
        font-weight: bold;
    }

    /* Keywords section */
    #tiptop-keywords {
        font-style: italic;
        color: #546e7a;
        background-color: #f5f7fa;
        padding: 8px 12px;
        border-radius: 4px;
        display: inline-block;
        margin: 0;
        line-height: 1.5;
        border: 1px solid #e0e6ed;
    }

    /* Tips section */
    #tiptop-tips {
        list-style-type: none;
        padding-left: 0;
        margin: 0;
    }

    #tiptop-tips li {
        margin-bottom: 12px;
        padding: 12px 15px;
        background-color: #f1f8fe;
        border-radius: 6px;
        border: 1px solid #d1e6fa;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    #tiptop-tips li:last-child {
        margin-bottom: 0;
    }

    #tiptop-tips li strong {
        color: #2980b9;
        display: block;
        margin-bottom: 5px;
        font-size: 15px;
    }

    /* Links section */
    #tiptop-links {
        list-style-type: none;
        padding-left: 0;
        margin: 0;
    }

    #tiptop-links li {
        margin-bottom: 10px;
        padding: 0;
    }

    #tiptop-links li:last-child {
        margin-bottom: 0;
    }

    #tiptop-links li a {
        display: block;
        padding: 10px 15px;
        background-color: #f5f7fa;
        border-radius: 6px;
        border: 1px solid #e0e6ed;
        transition: all 0.2s ease;
    }

    #tiptop-links li a:hover {
        background-color: #e8f4fd;
        border-color: #c1d9f0;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    /* Related Resources section */
    #tiptop-resources {
        list-style-type: none;
        padding-left: 0;
        margin: 0;
    }

    .tiptop-resource {
        margin-bottom: 12px;
        background-color: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #e0e6ed;
        overflow: hidden;
        transition: all 0.2s ease;
    }

    .tiptop-resource:last-child {
        margin-bottom: 0;
    }

    .tiptop-resource:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        border-color: #c1d9f0;
    }

    .tiptop-resource a {
        display: flex;
        align-items: center;
        padding: 12px;
        text-decoration: none;
        color: #2c3e50;
    }

    .tiptop-resource-image {
        width: 50px;
        height: 50px;
        object-fit: contain;
        margin-right: 12px;
        border-radius: 4px;
        background-color: #ffffff;
        padding: 4px;
        border: 1px solid #e0e6ed;
    }

    .tiptop-resource-content {
        flex: 1;
    }

    .tiptop-resource-title {
        font-weight: 600;
        margin-bottom: 4px;
        color: #2980b9;
    }

    .tiptop-resource-description {
        font-size: 12px;
        color: #546e7a;
        margin: 0;
    }

    .tiptop-resource-price {
        font-weight: 600;
        color: #2c3e50;
        margin-top: 4px;
        font-size: 13px;
    }

    .tiptop-affiliate-disclosure, .tiptop-affiliate-notice {
        font-size: 11px;
        color: #95a5a6;
        margin-top: 10px;
        text-align: left !important;
        padding: 0 10px;
    }

    .tiptop-affiliate-notice {
        font-size: 10px;
        font-style: italic;
        color: #7f8c8d;
        margin-top: 8px;
        text-align: right;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    /* Tips & Links items styling */
    #tiptop-resources-tips {
        list-style-type: none;
        padding-left: 0;
        margin: 0;
    }

    .tiptop-tip-item {
        margin-bottom: 12px;
        background-color: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #e0e6ed;
        overflow: hidden;
        transition: all 0.2s ease;
    }

    .tiptop-tip-item:last-child {
        margin-bottom: 0;
    }

    .tiptop-tip-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        border-color: #c1d9f0;
    }

    .tiptop-tip-content {
        padding: 12px;
    }

    .tiptop-tip-title {
        font-weight: 600;
        margin-bottom: 4px;
        color: #2980b9;
    }

    .tiptop-tip-description {
        font-size: 13px;
        color: #546e7a;
        margin-bottom: 8px;
    }

    .tiptop-tip-link {
        margin-top: 8px;
    }

    .tiptop-tip-link a {
        display: inline-block;
        padding: 5px 10px;
        background-color: #e8f4fd;
        border-radius: 4px;
        font-size: 13px;
        border: 1px solid #c1d9f0;
        transition: all 0.2s ease;
    }

    .tiptop-tip-link a:hover {
        background-color: #d1e6fa;
        transform: translateY(-1px);
    }

    /* Footer styles */
    .tiptop-footer {
        margin-top: 20px;
        padding-top: 15px;
        border-top: 1px solid rgba(0, 0, 0, 0.1);
        font-size: 11px;
        color: #95a5a6;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .tiptop-copyright {
        opacity: 0.8;
    }

    .tiptop-footer-links a {
        color: #95a5a6;
        text-decoration: none;
        transition: color 0.2s ease;
    }

    .tiptop-footer-links a:hover {
        color: #3498db;
        text-decoration: underline;
    }

    .tiptop-footer-divider {
        margin: 0 5px;
        opacity: 0.5;
    }

    /* Link styling */
    #tiptop-panel a {
        color: #2980b9;
        text-decoration: none;
        transition: all 0.2s ease;
        font-weight: 500;
    }

    #tiptop-panel a:hover {
        color: #3498db;
    }

    /* Tip links specific styling */
    #tiptop-tips li a {
        display: inline-block;
        margin-top: 8px;
        padding: 5px 10px;
        background-color: #e8f4fd;
        border-radius: 4px;
        font-size: 13px;
        border: 1px solid #c1d9f0;
    }

    #tiptop-tips li a:hover {
        background-color: #d1e6fa;
    }

    /* Close button */
    #tiptop-close {
        position: absolute;
        top: 12px;
        right: 12px;
        background-color: rgba(255, 255, 255, 0.3);
        border: none;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        cursor: pointer;
        color: #ffffff;
        transition: all 0.2s ease;
        z-index: 1;
    }

    #tiptop-close:hover {
        background-color: rgba(255, 255, 255, 0.5);
        transform: rotate(90deg);
    }

    /* Cursor for typewriter effect */
    .tiptop-cursor {
        display: inline-block;
        margin-left: 2px;
        font-weight: bold;
        color: #3498db;
        animation: tiptop-blink 1s step-end infinite;
        position: relative;
        top: -1px;
    }

    @keyframes tiptop-blink {
        0%, 100% { opacity: 1; }
        50% { opacity: 0; }
    }

    /* Animation classes */
    .tiptop-fade-in {
        animation: tiptop-fade-in 0.5s ease-in-out;
    }

    /* Highlight pulse animation for social feature title */
    .tiptop-highlight-pulse {
        position: relative;
        color: #3498db;
        font-weight: 600;
        animation: tiptop-pulse-color 3s ease-in-out infinite alternate;
        text-shadow: 0 0 1px rgba(52, 152, 219, 0.3);
    }

    @keyframes tiptop-pulse-color {
        0% {
            color: #3498db;
            text-shadow: 0 0 1px rgba(52, 152, 219, 0.3);
        }
        50% {
            color: #2980b9;
            text-shadow: 0 0 4px rgba(52, 152, 219, 0.5);
        }
        100% {
            color: #3498db;
            text-shadow: 0 0 1px rgba(52, 152, 219, 0.3);
        }
    }

    /* Loading indicator */
    .tiptop-loading {
        text-align: center;
        padding: 30px 0;
        background-color: #ffffff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        border: 1px solid #e1e4e8;
        margin: 10px 0;
    }

    .tiptop-loading p {
        margin-bottom: 20px;
        color: #2c3e50;
        font-weight: 500;
        font-size: 15px;
    }

    .tiptop-loading-dots {
        display: flex;
        justify-content: center;
        gap: 10px;
    }

    .tiptop-loading-dots .dot {
        width: 12px;
        height: 12px;
        background-color: #3498db;
        border-radius: 50%;
        display: inline-block;
        animation: tiptop-dot-pulse 1.4s infinite ease-in-out both;
        box-shadow: 0 0 5px rgba(52, 152, 219, 0.5);
    }

    .tiptop-loading-dots .dot:nth-child(1) {
        animation-delay: -0.32s;
        background-color: #3498db;
    }

    .tiptop-loading-dots .dot:nth-child(2) {
        animation-delay: -0.16s;
        background-color: #2980b9;
    }

    .tiptop-loading-dots .dot:nth-child(3) {
        background-color: #1f6da8;
    }
`;
document.head.appendChild(style);

let tiptopButton = null;
let tiptopPanel = null;
let isLoading = false;
let cachedResponses = {}; // Cache for API responses
let cachedAIResponses = {}; // Cache for AI question responses

// Clear cache on extension reload to prevent automatic AI content display
function clearAICache() {
    cachedResponses = {};
    cachedAIResponses = {};
    console.log('AI cache cleared on extension reload');
}

// Function to ensure AI content is hidden when toggle is off
function ensureAIContentHidden() {
    const aiContentSections = document.getElementById('tiptop-ai-content-sections');
    const aiLoadingSection = document.getElementById('tiptop-ai-loading');
    const aiErrorSection = document.getElementById('tiptop-ai-error');
    const aiToggle = document.getElementById('tiptop-ai-toggle');

    if (aiToggle && !aiToggle.checked) {
        if (aiContentSections) {
            aiContentSections.style.display = 'none';
        }
        if (aiLoadingSection) {
            aiLoadingSection.style.display = 'none';
        }
        if (aiErrorSection) {
            aiErrorSection.style.display = 'none';
        }
        console.log('All AI content hidden - toggle is off');
    }
}
let isDraggingButton = false;
let isDraggingPanel = false;
let dragOffsetX = 0;
let dragOffsetY = 0;
let panelWasOpenBeforeDrag = false;
let dragStartTime = 0;
let dragStartPos = { x: 0, y: 0 };
let hasMoved = false;

// --- UI Creation ---

function createTipTopButton() {
    const existingButton = document.getElementById('tiptop-button');
    if (existingButton) {
        console.log('TipTop button already exists, skipping creation');
        tiptopButton = existingButton; // Ensure we have reference
        return; // Already exists
    }

    console.log('Creating TipTop button...');
    tiptopButton = document.createElement('button');
    tiptopButton.id = 'tiptop-button';

    // Using the actual tiptop0.png image file as the button itself
    tiptopButton.innerHTML = `
        <img src="${chrome.runtime.getURL('images/tiptop0.png')}" alt="TipTop Lightbulb" style="width: 52px; height: 52px; object-fit: contain; filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2)); transition: filter 0.3s ease, transform 0.3s ease;">
    `;

    // Style the button - using transparent background to let the image be the button
    tiptopButton.style.position = 'fixed';
    tiptopButton.style.zIndex = '9999';
    tiptopButton.style.width = '52px';
    tiptopButton.style.height = '52px';
    tiptopButton.style.padding = '0';
    tiptopButton.style.display = 'flex';
    tiptopButton.style.alignItems = 'center';
    tiptopButton.style.justifyContent = 'center';
    tiptopButton.style.backgroundColor = 'transparent';
    tiptopButton.style.border = 'none';
    tiptopButton.style.cursor = 'pointer'; // Change cursor to show hand icon
    // No transition on the button itself to avoid conflicts with positioning

    // Load saved position from storage or use default
    chrome.storage.local.get(['tiptopButtonPosition'], function(result) {
        if (result.tiptopButtonPosition) {
            // Use saved position
            tiptopButton.style.left = result.tiptopButtonPosition.left;
            tiptopButton.style.top = result.tiptopButtonPosition.top;
            // Remove right/bottom if they were previously set
            tiptopButton.style.right = '';
            tiptopButton.style.bottom = '';
        } else {
            // Use default position (bottom right corner)
            tiptopButton.style.right = '20px';
            tiptopButton.style.bottom = '20px';
            // Clear any top/left values
            tiptopButton.style.top = '';
            tiptopButton.style.left = '';
        }
    });

    // Add hover effect for the image button
    tiptopButton.onmouseover = () => {
        if (!isDraggingButton) {
            tiptopButton.querySelector('img').style.transform = 'scale(1.05)';
            tiptopButton.querySelector('img').style.filter = 'drop-shadow(0 4px 8px rgba(0,0,0,0.3)) brightness(1.1)';
        }
    };

    tiptopButton.onmouseout = () => {
        if (!isDraggingButton) {
            tiptopButton.querySelector('img').style.transform = 'scale(1)';
            tiptopButton.querySelector('img').style.filter = 'drop-shadow(0 2px 4px rgba(0,0,0,0.2))';
        }
    };

    // Make the button draggable
    tiptopButton.addEventListener('mousedown', startButtonDrag);

    // Remove the click handler - we'll handle clicks in the mouseup event
    // to better distinguish between clicks and drags

    try {
        document.body.appendChild(tiptopButton);
        console.log("✅ TipTop button created and added to DOM");

        // Verify button was added successfully
        const verifyButton = document.getElementById('tiptop-button');
        if (!verifyButton) {
            console.error("❌ Button creation failed - not found in DOM after appendChild");
        }
    } catch (error) {
        console.error("❌ Error creating TipTop button:", error);
    }
}

function createTipTopPanel() {
    if (document.getElementById('tiptop-panel')) return; // Already exists

    tiptopPanel = document.createElement('div');
    tiptopPanel.id = 'tiptop-panel';

    // Set essential styles that aren't in CSS
    tiptopPanel.style.position = 'fixed';
    tiptopPanel.style.width = '350px';
    tiptopPanel.style.maxHeight = '450px'; // Slightly taller for better content display
    tiptopPanel.style.zIndex = '9998';
    tiptopPanel.style.overflowY = 'auto';
    tiptopPanel.style.padding = '18px';
    tiptopPanel.style.display = 'none'; // Initially hidden
    tiptopPanel.style.fontSize = '14px';
    tiptopPanel.style.cursor = 'default'; // Use default cursor for panel content

    // Position will be set dynamically based on button position

    // Use cleaner HTML structure without inline styles (using our CSS classes)
    tiptopPanel.innerHTML = `
        <h3>TipTop Insights</h3>
        <div id="tiptop-content">
            <div class="tiptop-loading">
                <p>Loading insights...</p>
                <div class="tiptop-loading-dots">
                    <span class="dot"></span>
                    <span class="dot"></span>
                    <span class="dot"></span>
                </div>
            </div>
        </div>
        <button id="tiptop-close">&times;</button>
    `;

    document.body.appendChild(tiptopPanel);
    console.log("TipTop panel created.");

    // Add close button functionality
    document.getElementById('tiptop-close').onclick = () => {
        tiptopPanel.style.display = 'none';
    };

    // Make the panel draggable
    tiptopPanel.addEventListener('mousedown', startPanelDrag);

    // Prevent panel drag when clicking on specific elements
    const preventDragElements = ['a', 'button', '#tiptop-close', '#tiptop-content'];
    preventDragElements.forEach(selector => {
        const elements = tiptopPanel.querySelectorAll(selector);
        elements.forEach(el => {
            el.addEventListener('mousedown', e => e.stopPropagation());
        });
    });
}

// --- UI Interaction ---

function toggleTipTopPanel() {
    if (!tiptopPanel) {
        createTipTopPanel(); // Create if it doesn't exist
    }

    if (tiptopPanel.style.display === 'none') {
        tiptopPanel.style.display = 'block';

        // Add a flag to indicate the panel was just reopened
        // This will be used to ensure proper scrolling in the chat
        tiptopPanel.dataset.justReopened = 'true';

        // Always fetch data when panel is opened
        fetchTipTopData();

        // Position the panel relative to the button's current position or use saved position
        updatePanelPosition();

        // Restore the social connection state when reopening the panel
        // This ensures the connection is properly restored and the UI is updated
        if (window.TipTopSocial) {
            console.log('Restoring social features after reopening panel');

            // Use the dedicated panel reopen handler if available
            if (window.TipTopSocial.handlePanelReopen) {
                console.log('Using dedicated panel reopen handler');

                // First, make sure social features are initialized
                if (window.TipTopSocial.initialize) {
                    window.TipTopSocial.initialize();
                }

                // Short delay to ensure initialization completes
                setTimeout(() => {
                    // Then handle panel reopening
                    window.TipTopSocial.handlePanelReopen();

                    // Set a flag to indicate we've handled the panel reopening
                    tiptopPanel.dataset.socialHandled = 'true';
                }, 50);

                // Set up a backup plan in case the chat is still empty after a delay
                setTimeout(() => {
                    const chatLogElement = document.getElementById('tiptop-chat-log');
                    if (chatLogElement && chatLogElement.children.length === 0) {
                        console.log('Chat still empty after delay, trying fallback method');

                        // Try toggling social features as a last resort
                        const socialToggle = document.getElementById('tiptop-social-toggle');
                        if (socialToggle && socialToggle.checked && window.TipTopSocial.toggleSocialFeatures) {
                            console.log('Using toggle fallback to refresh connection');
                            window.TipTopSocial.toggleSocialFeatures(false);

                            setTimeout(() => {
                                window.TipTopSocial.toggleSocialFeatures(true);
                            }, 100);
                        }
                    }
                }, 1000);
            } else {
                // Fallback to the old method if the new handler isn't available
                console.log('Fallback: Using legacy method to restore social features');
                const socialToggle = document.getElementById('tiptop-social-toggle');
                if (socialToggle && socialToggle.checked && window.TipTopSocial.toggleSocialFeatures) {
                    console.log('Toggling social features off and on to refresh connection');
                    window.TipTopSocial.toggleSocialFeatures(false);

                    // Short delay before toggling back on
                    setTimeout(() => {
                        window.TipTopSocial.toggleSocialFeatures(true);
                    }, 100);
                }

                // Force scroll chat to bottom regardless of social features state
                setTimeout(() => {
                    if (window.TipTopSocial && window.TipTopSocial.forceScrollChatOnReopen) {
                        console.log('Calling dedicated function to force scroll chat to bottom');
                        window.TipTopSocial.forceScrollChatOnReopen();
                    } else {
                        // Direct scroll as fallback
                        const chatLogElement = document.getElementById('tiptop-chat-log');
                        if (chatLogElement) {
                            console.log('Forcing scroll to bottom after reopening panel (direct method)');
                            chatLogElement.scrollTop = chatLogElement.scrollHeight;
                        }
                    }
                }, 500);
            }
        }
    } else {
        tiptopPanel.style.display = 'none';
    }
}

// Function to handle button click (called from mouseup if not dragged)
function handleButtonClick() {
    console.log('TipTop button clicked - calling toggleTipTopPanel');
    toggleTipTopPanel();
}

// Function to start dragging the button
function startButtonDrag(e) {
    // Prevent default behavior and bubbling
    e.preventDefault();
    e.stopPropagation();

    // Check if panel is open before dragging
    panelWasOpenBeforeDrag = tiptopPanel && tiptopPanel.style.display !== 'none';

    // Get the current button position
    const buttonRect = tiptopButton.getBoundingClientRect();

    // Calculate the offset of the mouse cursor from the button's top-left corner
    dragOffsetX = e.clientX - buttonRect.left;
    dragOffsetY = e.clientY - buttonRect.top;

    // Record drag start time and position for distinguishing clicks from drags
    dragStartTime = Date.now();
    dragStartPos = { x: e.clientX, y: e.clientY };
    hasMoved = false;

    // Set dragging flag
    isDraggingButton = true;

    // Add event listeners for drag and end drag events
    document.addEventListener('mousemove', dragButton);
    document.addEventListener('mouseup', stopButtonDrag);

    // Change cursor during drag to grabbing hand
    document.body.style.cursor = 'grabbing';

    // Disable image hover effects during drag
    tiptopButton.querySelector('img').style.transition = 'none';
}

// Function to drag the button
function dragButton(e) {
    if (!isDraggingButton) return;

    // Prevent default behavior
    e.preventDefault();

    // Calculate new position based on mouse position and initial offset
    let newLeft = e.clientX - dragOffsetX;
    let newTop = e.clientY - dragOffsetY;

    // Get viewport dimensions
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // Ensure the button stays within the viewport boundaries
    newLeft = Math.max(0, Math.min(newLeft, viewportWidth - tiptopButton.offsetWidth));
    newTop = Math.max(0, Math.min(newTop, viewportHeight - tiptopButton.offsetHeight));

    // Check if we've moved enough to consider this a drag rather than a click
    const moveX = Math.abs(e.clientX - dragStartPos.x);
    const moveY = Math.abs(e.clientY - dragStartPos.y);
    if (moveX > 3 || moveY > 3) { // 3px threshold for movement
        hasMoved = true;
    }

    // Update button position
    tiptopButton.style.left = newLeft + 'px';
    tiptopButton.style.top = newTop + 'px';
    tiptopButton.style.right = '';
    tiptopButton.style.bottom = '';

    // Update panel position if it's visible
    if (tiptopPanel && tiptopPanel.style.display !== 'none') {
        updatePanelPosition();
    }
}

// Function to stop dragging the button
function stopButtonDrag(e) {
    if (!isDraggingButton) return;

    // Prevent default behavior
    e.preventDefault();
    e.stopPropagation();

    // Remove event listeners
    document.removeEventListener('mousemove', dragButton);
    document.removeEventListener('mouseup', stopButtonDrag);

    // Reset cursor
    document.body.style.cursor = '';

    // Reset dragging flag
    isDraggingButton = false;

    // Restore image transition effects
    tiptopButton.querySelector('img').style.transition = 'filter 0.3s ease, transform 0.3s ease';

    // Save the button position to storage
    saveButtonPosition();

    // If panel was open before dragging started, make sure it's still open
    if (panelWasOpenBeforeDrag && tiptopPanel) {
        tiptopPanel.style.display = 'block';
        updatePanelPosition();
    }

    // Check if this was a click (short duration, minimal movement) or a drag
    const dragDuration = Date.now() - dragStartTime;
    console.log('Button interaction:', { hasMoved, dragDuration, threshold: 200 });
    if (!hasMoved && dragDuration < 200) { // Less than 200ms and no significant movement = click
        console.log('Detected as click - calling handleButtonClick');
        handleButtonClick();
    } else {
        console.log('Detected as drag - not calling handleButtonClick');
    }
}

// Function to save button position to storage
function saveButtonPosition() {
    // Only save position if using left/top positioning (i.e., has been dragged)
    if (tiptopButton.style.left && tiptopButton.style.top) {
        const position = {
            left: tiptopButton.style.left,
            top: tiptopButton.style.top
        };

        chrome.storage.local.set({ tiptopButtonPosition: position }, function() {
            console.log('Button position saved:', position);
        });
    }
}

// Function to save panel position to storage
function savePanelPosition() {
    if (!tiptopPanel) return;

    // Check if panel is using left/top or right/top positioning
    if (tiptopPanel.style.left) {
        const position = {
            left: tiptopPanel.style.left,
            top: tiptopPanel.style.top
        };

        chrome.storage.local.set({ tiptopPanelPosition: position }, function() {
            console.log('Panel position saved (left/top):', position);
        });
    } else if (tiptopPanel.style.right) {
        // For right-side positioning, we'll convert to left for consistency
        const panelRect = tiptopPanel.getBoundingClientRect();
        const viewportWidth = window.innerWidth;

        const position = {
            left: (viewportWidth - panelRect.width - parseFloat(tiptopPanel.style.right)) + 'px',
            top: tiptopPanel.style.top
        };

        chrome.storage.local.set({ tiptopPanelPosition: position }, function() {
            console.log('Panel position saved (converted from right):', position);
        });
    }
}

// Function to start dragging the panel
function startPanelDrag(e) {
    // Don't drag if clicking on interactive elements
    if (e.target.tagName.toLowerCase() === 'a' ||
        e.target.tagName.toLowerCase() === 'button' ||
        e.target.id === 'tiptop-close' ||
        e.target.id === 'tiptop-content') {
        return;
    }

    // Prevent default behavior and bubbling
    e.preventDefault();
    e.stopPropagation();

    // Get the current panel position
    const panelRect = tiptopPanel.getBoundingClientRect();

    // Calculate the offset of the mouse cursor from the panel's top-left corner
    dragOffsetX = e.clientX - panelRect.left;
    dragOffsetY = e.clientY - panelRect.top;

    // Set dragging flag
    isDraggingPanel = true;

    // Add event listeners for drag and end drag events
    document.addEventListener('mousemove', dragPanel);
    document.addEventListener('mouseup', stopPanelDrag);

    // Change cursor during drag to grabbing hand
    document.body.style.cursor = 'grabbing';
}

// Function to drag the panel
function dragPanel(e) {
    if (!isDraggingPanel) return;

    // Prevent default behavior
    e.preventDefault();

    // Calculate new position based on mouse position and initial offset
    let newLeft = e.clientX - dragOffsetX;
    let newTop = e.clientY - dragOffsetY;

    // Get viewport dimensions
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // Ensure the panel stays within the viewport boundaries
    newLeft = Math.max(0, Math.min(newLeft, viewportWidth - tiptopPanel.offsetWidth));
    newTop = Math.max(0, Math.min(newTop, viewportHeight - tiptopPanel.offsetHeight));

    // Update panel position - always use left/top when dragging for consistency
    tiptopPanel.style.left = newLeft + 'px';
    tiptopPanel.style.top = newTop + 'px';
    tiptopPanel.style.right = '';
    tiptopPanel.style.bottom = '';
}

// Function to stop dragging the panel
function stopPanelDrag() {
    if (!isDraggingPanel) return;

    // Remove event listeners
    document.removeEventListener('mousemove', dragPanel);
    document.removeEventListener('mouseup', stopPanelDrag);

    // Reset cursor
    document.body.style.cursor = '';

    // Reset dragging flag
    isDraggingPanel = false;

    // Save the panel position
    savePanelPosition();
}

// Function to update panel position relative to button
function updatePanelPosition() {
    if (!tiptopPanel || !tiptopButton) return;

    // Check if we have a saved panel position
    chrome.storage.local.get(['tiptopPanelPosition'], function(result) {
        if (result.tiptopPanelPosition) {
            // Use saved position
            tiptopPanel.style.left = result.tiptopPanelPosition.left;
            tiptopPanel.style.top = result.tiptopPanelPosition.top;
            tiptopPanel.style.right = '';
            tiptopPanel.style.bottom = '';

            // Verify the panel is still within viewport
            setTimeout(() => {
                ensurePanelIsVisible();
            }, 0);
            return;
        }

        // If no saved position, position on the right side of the screen
        positionPanelOnRightSide();
    });
}

// Function to position panel on the right side of the screen (initial position)
function positionPanelOnRightSide() {
    if (!tiptopPanel) return;

    // Only need viewport height for vertical centering
    const viewportHeight = window.innerHeight;

    // Position on the right side, vertically centered
    tiptopPanel.style.right = '20px';
    tiptopPanel.style.top = Math.max(20, (viewportHeight - 450) / 2) + 'px';
    tiptopPanel.style.left = '';
    tiptopPanel.style.bottom = '';

    // Ensure panel is visible
    setTimeout(() => {
        ensurePanelIsVisible();
    }, 0);
}

// Function to position panel relative to button
function positionPanelRelativeToButton() {
    if (!tiptopPanel || !tiptopButton) return;

    // Get dimensions and positions
    const buttonRect = tiptopButton.getBoundingClientRect();
    const panelWidth = parseInt(tiptopPanel.style.width) || 350;
    const panelHeight = tiptopPanel.offsetHeight || 450; // Use actual height or estimate
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // Reset any previously set positioning
    tiptopPanel.style.bottom = '';
    tiptopPanel.style.right = '';

    // Determine the best position for the panel
    let panelPosition = 'top'; // Default try to position above button

    // Check if there's enough space above the button
    if (buttonRect.top < panelHeight + 20) {
        // Not enough space above, try below
        panelPosition = 'bottom';

        // If not enough space below either, try right or left
        if (buttonRect.bottom + panelHeight + 20 > viewportHeight) {
            if (buttonRect.right + panelWidth + 20 < viewportWidth) {
                panelPosition = 'right';
            } else if (buttonRect.left > panelWidth + 20) {
                panelPosition = 'left';
            }
            // If no good position, default to centered on screen
            else {
                panelPosition = 'center';
            }
        }
    }

    // Position based on the determined best location
    switch (panelPosition) {
        case 'top':
            // Position above button
            let topPanelLeft = buttonRect.left + (buttonRect.width / 2) - (panelWidth / 2);
            topPanelLeft = Math.max(20, Math.min(topPanelLeft, viewportWidth - panelWidth - 20));
            tiptopPanel.style.left = topPanelLeft + 'px';
            tiptopPanel.style.top = Math.max(20, buttonRect.top - panelHeight - 10) + 'px';
            break;

        case 'bottom':
            // Position below button
            let bottomPanelLeft = buttonRect.left + (buttonRect.width / 2) - (panelWidth / 2);
            bottomPanelLeft = Math.max(20, Math.min(bottomPanelLeft, viewportWidth - panelWidth - 20));
            tiptopPanel.style.left = bottomPanelLeft + 'px';
            tiptopPanel.style.top = Math.min(viewportHeight - panelHeight - 20, buttonRect.bottom + 10) + 'px';
            break;

        case 'right':
            // Position to the right of button
            tiptopPanel.style.left = Math.min(viewportWidth - panelWidth - 20, buttonRect.right + 10) + 'px';
            let rightPanelTop = buttonRect.top + (buttonRect.height / 2) - (panelHeight / 2);
            rightPanelTop = Math.max(20, Math.min(rightPanelTop, viewportHeight - panelHeight - 20));
            tiptopPanel.style.top = rightPanelTop + 'px';
            break;

        case 'left':
            // Position to the left of button
            tiptopPanel.style.left = Math.max(20, buttonRect.left - panelWidth - 10) + 'px';
            let leftPanelTop = buttonRect.top + (buttonRect.height / 2) - (panelHeight / 2);
            leftPanelTop = Math.max(20, Math.min(leftPanelTop, viewportHeight - panelHeight - 20));
            tiptopPanel.style.top = leftPanelTop + 'px';
            break;

        case 'center':
            // Center on screen as fallback
            tiptopPanel.style.left = Math.max(20, (viewportWidth - panelWidth) / 2) + 'px';
            tiptopPanel.style.top = Math.max(20, (viewportHeight - panelHeight) / 2) + 'px';
            break;
    }

    // Final check to ensure panel is visible
    setTimeout(() => {
        ensurePanelIsVisible();
    }, 0);
}

// Function to ensure panel is visible within viewport
function ensurePanelIsVisible() {
    if (!tiptopPanel) return;

    const panelRect = tiptopPanel.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // If panel is off-screen, position it on the right side
    if (panelRect.right > viewportWidth || panelRect.bottom > viewportHeight ||
        panelRect.left < 0 || panelRect.top < 0) {
        // Position on the right side
        tiptopPanel.style.right = '20px';
        tiptopPanel.style.left = '';
        tiptopPanel.style.top = Math.max(20, (viewportHeight - panelRect.height) / 2) + 'px';
    }
}

// --- Data Fetching & Display ---

function fetchTipTopData() {
    if (isLoading) return;
    isLoading = true;

    // First, display the initial panel with social features
    displayInitialPanelWithSocial();

    // Reset loading flag since we're not fetching AI data by default
    isLoading = false;
}

// New function to fetch AI data only when AI toggle is enabled
function fetchAIData() {
    // Double-check that AI toggle is enabled before fetching
    const aiToggle = document.getElementById('tiptop-ai-toggle');
    if (!aiToggle || !aiToggle.checked) {
        console.log('fetchAIData called but AI toggle is disabled - aborting');
        return;
    }

    if (isLoading) return;
    isLoading = true;

    console.log("AI toggle enabled - fetching AI data...");

    // Check if we have a cached response for the current URL
    const currentUrl = window.location.href;
    if (cachedResponses[currentUrl]) {
        console.log("Using cached TipTop AI data");

        // Show loading indicator in the AI content sections
        updateAIContentLoadingState(true);

        // Use a small timeout to show the loading animation briefly before displaying cached data
        setTimeout(() => {
            isLoading = false;
            displayTipTopData(cachedResponses[currentUrl], true); // Pass true to indicate cached data
        }, 300);
        return;
    }

    console.log("Requesting TipTop AI data from background script...");

    // Show loading indicators in the AI content sections
    updateAIContentLoadingState(true);

    // Add debouncing to prevent multiple rapid requests
    setTimeout(() => {
        chrome.runtime.sendMessage({ type: 'GET_TIPTOP_DATA' }, (response) => {
            isLoading = false; // Reset loading flag regardless of outcome
            if (chrome.runtime.lastError) {
                console.error("Error sending/receiving message:", chrome.runtime.lastError);
                displayAIContentError(chrome.runtime.lastError.message || 'Communication failed');
                return;
            }

            console.log("Received response from background:", response);
            if (response && response.success) {
                displayTipTopData(response.data, false); // Pass false to indicate fresh data
            } else {
                displayAIContentError(response?.error || 'Failed to get data');
            }
        });
    }, 100); // Small delay to debounce rapid requests
}

// Function to display the initial panel with social features
function displayInitialPanelWithSocial() {
    // Create the initial structure with social features and loading placeholders for AI content
    let initialContent = '';

    // Add social collaboration section at the top
    initialContent += `
        <div id="tiptop-collaboration" class="tiptop-content-section">
            <div class="tiptop-collaboration-header">
                <div class="tiptop-collaboration-title tiptop-highlight-pulse">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                        <circle cx="9" cy="7" r="4"></circle>
                        <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                        <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                    </svg>
                    Who's on the same page?
                </div>
                <label class="tiptop-collaboration-toggle">
                    <input type="checkbox" id="tiptop-social-toggle">
                    <span class="tiptop-toggle-slider"></span>
                </label>
            </div>
            <div class="tiptop-collaboration-status" style="display: none;">
                <span class="tiptop-status-disconnected">●</span> Connecting...
            </div>
            <div id="tiptop-collaboration-notification" class="tiptop-notification"></div>
            <div class="tiptop-collaboration-loading" id="tiptop-collaboration-loading" style="display: none;">
                <div class="tiptop-loading-content">
                    <div class="tiptop-loading-spinner"></div>
                    <div class="tiptop-loading-text">Connecting to other users<span class="tiptop-loading-dots">...</span></div>
                </div>
            </div>
            <div class="tiptop-collaboration-controls" style="display: none;">
                <div class="tiptop-tabs">
                    <button class="tiptop-tab-button active" data-tab="chat">Chat & History</button>
                    <button class="tiptop-tab-button" data-tab="users">Peers</button>
                </div>
                <div class="tiptop-tab-content active" id="tiptop-tab-chat">
                    <div class="tiptop-chat-container" id="tiptop-chat-container">
                        <div class="tiptop-no-users">Chat will be available once connected.</div>
                    </div>

                    <!-- Chat history toggle -->
                    <div class="tiptop-chat-history-toggle">
                        <button id="tiptop-show-history" class="tiptop-history-button">
                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <circle cx="12" cy="12" r="10"></circle>
                                <polyline points="12 6 12 12 16 14"></polyline>
                            </svg>
                            Show Page History
                        </button>
                    </div>

                    <!-- Chat history section (initially hidden) -->
                    <div id="tiptop-chat-history" class="tiptop-chat-history" style="display: none;">
                        <div class="tiptop-history-header">
                            <h4>Page History</h4>
                            <button id="tiptop-hide-history" class="tiptop-close-button">×</button>
                        </div>
                        <div id="tiptop-history-content" class="tiptop-history-content">
                            <div class="tiptop-no-history">No history available for this page yet.</div>
                        </div>
                    </div>

                    <div class="tiptop-chat-input">
                        <input type="text" id="tiptop-chat-input-1" placeholder="Type a message...">
                        <button id="tiptop-chat-send-1">Send</button>
                    </div>
                </div>
                <div class="tiptop-tab-content" id="tiptop-tab-users">
                    <div class="tiptop-user-list" id="tiptop-user-list">
                        <div class="tiptop-no-users">No other users viewing this page</div>
                    </div>
                </div>
                <div class="tiptop-tab-content" id="tiptop-tab-chat">
                    <div class="tiptop-chat-container" id="tiptop-chat-container">
                        <div class="tiptop-no-users">Chat will be available once connected.</div>
                    </div>

                    <!-- Chat history toggle -->
                    <div class="tiptop-chat-history-toggle">
                        <button id="tiptop-show-history" class="tiptop-history-button">
                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <circle cx="12" cy="12" r="10"></circle>
                                <polyline points="12 6 12 12 16 14"></polyline>
                            </svg>
                            Show Page History
                        </button>
                    </div>

                    <!-- Chat history section (initially hidden) -->
                    <div id="tiptop-chat-history" class="tiptop-chat-history" style="display: none;">
                        <div class="tiptop-history-header">
                            <h4>Page History</h4>
                            <button id="tiptop-hide-history" class="tiptop-close-button">×</button>
                        </div>
                        <div id="tiptop-history-content" class="tiptop-history-content">
                            <div class="tiptop-no-history">No history available for this page yet.</div>
                        </div>
                    </div>

                    <div class="tiptop-chat-input">
                        <input type="text" id="tiptop-chat-input-2" placeholder="Type a message...">
                        <button id="tiptop-chat-send-2">Send</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Add AI content sections with loading indicators
    initialContent += `
        <div id="tiptop-ai-content" class="tiptop-content-section">
            <div class="tiptop-collaboration-header">
                <div class="tiptop-collaboration-title">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M12 2a10 10 0 0 1 10 10c0 5.523-4.477 10-10 10S2 17.523 2 12 6.477 2 12 2"></path>
                        <path d="M16.24 7.76a6 6 0 0 1 0 8.49m-8.48-.01a6 6 0 0 1 0-8.49m11.31-2.82a10 10 0 0 1 0 14.14m-14.14 0a10 10 0 0 1 0-14.14"></path>
                        <circle cx="12" cy="12" r="1"></circle>
                    </svg>
                    AI-Powered Insights
                </div>
                <label class="tiptop-collaboration-toggle">
                    <input type="checkbox" id="tiptop-ai-toggle">
                    <span class="tiptop-toggle-slider"></span>
                </label>
            </div>
            <div id="tiptop-ai-loading" style="display: none;">
                <div class="tiptop-loading">
                    <p>Analyzing page content...</p>
                    <div class="tiptop-loading-dots">
                        <span class="dot"></span>
                        <span class="dot"></span>
                        <span class="dot"></span>
                    </div>
                </div>
            </div>

            <div id="tiptop-ai-error" style="display: none; color: #721c24; background-color: #ffeaec; border: 1px solid #f5c6cb; box-shadow: 0 2px 8px rgba(220, 53, 69, 0.1);">
                <div style="display: flex; align-items: flex-start;">
                    <div style="margin-right: 10px; color: #dc3545; font-size: 20px;">⚠️</div>
                    <div>
                        <strong style="display: block; margin-bottom: 5px; color: #dc3545;">Error:</strong>
                        <span id="tiptop-ai-error-message">Failed to load AI content</span>
                    </div>
                </div>
            </div>

            <div id="tiptop-ai-content-sections" style="display: none;">
                <!-- These sections will be populated when AI data is received -->
                <div id="tiptop-summary-section" class="tiptop-content-section">
                    <h4>Summary</h4>
                    <p id="tiptop-summary"></p>
                </div>

                <div id="tiptop-ai-interaction-section" class="tiptop-content-section">
                    <h4>Ask Tiptop</h4>
                    <div class="tiptop-ai-interaction">
                        <div class="tiptop-ai-question-input">
                            <input type="text" id="tiptop-ai-question" placeholder="Ask a question about this page...">
                            <button id="tiptop-ai-ask">Ask</button>
                        </div>
                        <div id="tiptop-ai-response-container" style="display: none;">
                            <div class="tiptop-ai-response-header">
                                <strong>Tiptop:</strong>
                            </div>
                            <p id="tiptop-ai-response"></p>
                        </div>
                    </div>
                </div>

                <div id="tiptop-takeaways-section" class="tiptop-content-section">
                    <h4>Key Takeaways</h4>
                    <ul id="tiptop-takeaways"></ul>
                </div>

                <div id="tiptop-resources-tips-section" class="tiptop-content-section">
                    <h4>Tips & Links</h4>
                    <ul id="tiptop-resources-tips"></ul>
                </div>

                <div id="tiptop-resources-section" class="tiptop-content-section">
                    <h4>Related Resources</h4>
                    <ul id="tiptop-resources"></ul>
                    <div class="tiptop-affiliate-notice" style="text-align: left;">Some resources may contain affiliate links at no extra cost to you.</div>
                </div>
            </div>
        </div>
    `;

    // Add footer with copyright and links
    initialContent += `
        <div class="tiptop-footer">
            <div class="tiptop-copyright">© ${new Date().getFullYear()} TipTop</div>
            <div class="tiptop-footer-links">
                <a href="https://tiptop.qubitrhythm.com/staticHosting/privacy.html" target="_blank">Privacy</a>
                <span class="tiptop-footer-divider">|</span>
                <a href="https://tiptop.qubitrhythm.com/staticHosting/support.html" target="_blank">Support</a>
            </div>
        </div>
    `;

    // Update the panel with the initial structure
    updatePanelContent(initialContent);

    // Initialize social features
    if (document.getElementById('tiptop-collaboration')) {
        initializeSocialFeatures();
    }

    // Initialize AI features
    if (document.getElementById('tiptop-ai-content')) {
        initializeAIFeatures();
    }

    // Initialize AI interaction features
    if (document.getElementById('tiptop-ai-interaction-section')) {
        initializeAIInteraction();
    }

    // Ensure AI content is hidden if toggle is off
    setTimeout(() => {
        ensureAIContentHidden();
    }, 100);
}

// Function to update the loading state of AI content sections
function updateAIContentLoadingState(isLoading) {
    // Check if AI toggle is enabled before showing any AI content
    const aiToggle = document.getElementById('tiptop-ai-toggle');
    if (!aiToggle || !aiToggle.checked) {
        console.log('AI toggle is disabled, not updating AI loading state');
        return;
    }

    const loadingSection = document.getElementById('tiptop-ai-loading');
    const contentSections = document.getElementById('tiptop-ai-content-sections');
    const errorSection = document.getElementById('tiptop-ai-error');

    if (loadingSection && contentSections && errorSection) {
        if (isLoading) {
            loadingSection.style.display = 'block';
            contentSections.style.display = 'none';
            errorSection.style.display = 'none';
        } else {
            loadingSection.style.display = 'none';
            contentSections.style.display = 'block';
        }
    }
}

// Function to display an error in the AI content section
function displayAIContentError(errorMessage) {
    const loadingSection = document.getElementById('tiptop-ai-loading');
    const contentSections = document.getElementById('tiptop-ai-content-sections');
    const errorSection = document.getElementById('tiptop-ai-error');
    const errorMessageElement = document.getElementById('tiptop-ai-error-message');

    if (loadingSection && contentSections && errorSection && errorMessageElement) {
        loadingSection.style.display = 'none';
        contentSections.style.display = 'none';
        errorSection.style.display = 'block';
        errorMessageElement.textContent = errorMessage;
    }
}

async function displayTipTopData(data, isFromCache = false) {
    if (!tiptopPanel) return;

    // Check if AI toggle is enabled before displaying AI content
    const aiToggle = document.getElementById('tiptop-ai-toggle');
    if (!aiToggle || !aiToggle.checked) {
        console.log('AI toggle is disabled, not displaying AI content');
        return;
    }

    // Cache the response using URL as key if not already cached
    const currentUrl = window.location.href;
    if (!cachedResponses[currentUrl] && !isFromCache) {
        cachedResponses[currentUrl] = data;
        // Limit cache size to prevent memory issues
        const cacheKeys = Object.keys(cachedResponses);
        if (cacheKeys.length > 10) {
            delete cachedResponses[cacheKeys[0]];
        }
    }

    // Signal that AI content is ready to be displayed
    updateAIContentLoadingState(false);

    // Add a notice if this is mock data
    if (data.isMockData || data.isFallbackMockData) {
        let noticeTitle = data.isFallbackMockData ? "API Error - Using Mock Data" : "Note";
        let noticeColor = data.isFallbackMockData ? "#dc3545" : "#ffc107";
        let noticeBgColor = data.isFallbackMockData ? "#f8d7da" : "#fff9e6";
        let noticeBorderColor = data.isFallbackMockData ? "#f5c6cb" : "#ffe8a1";
        let noticeBoxShadow = data.isFallbackMockData ? "rgba(220, 53, 69, 0.1)" : "rgba(255, 193, 7, 0.1)";
        let noticeTextColor = data.isFallbackMockData ? "#721c24" : "#856404";
        let noticeIcon = data.isFallbackMockData ? "⚠️" : "ℹ️";

        let noticeMessage = data.isFallbackMockData
            ? `The TipTop API request failed with error: <strong>${data.apiErrorMessage || 'Unknown error'}</strong>. Showing mock data instead.`
            : `Showing mock data because the TipTop API is currently unavailable due to SSL certificate issues.`;

        const noticeElement = document.createElement('div');
        noticeElement.className = 'tiptop-content-section';
        noticeElement.style.backgroundColor = noticeBgColor;
        noticeElement.style.color = noticeTextColor;
        noticeElement.style.border = `1px solid ${noticeBorderColor}`;
        noticeElement.style.boxShadow = `0 2px 8px ${noticeBoxShadow}`;
        noticeElement.style.marginBottom = '15px';

        noticeElement.innerHTML = `
            <div style="display: flex; align-items: flex-start;">
                <div style="margin-right: 10px; color: ${noticeColor}; font-size: 20px;">${noticeIcon}</div>
                <div>
                    <strong style="display: block; margin-bottom: 5px; color: ${noticeTextColor};">${noticeTitle}:</strong>
                    <p style="margin: 0 0 8px 0;">${noticeMessage}</p>
                    ${!data.isFallbackMockData ? `
                    <p style="margin: 0 0 8px 0;">The server is using a self-signed certificate: <code style="background: rgba(0,0,0,0.05); padding: 2px 4px; border-radius: 3px; font-size: 12px;">O=Acme Co; CN=Kubernetes Ingress Controller Fake Certificate</code></p>
                    <p style="margin: 0;">To fix this issue, a proper SSL certificate needs to be installed on the server.</p>
                    ` : ''}
                </div>
            </div>
        `;

        // Insert the notice at the top of the AI content section
        const aiContentSection = document.getElementById('tiptop-ai-content');
        if (aiContentSection) {
            aiContentSection.insertBefore(noticeElement, aiContentSection.firstChild.nextSibling);
        }
    }

    // Add reading time if available
    if (data.readingTime) {
        const readingTimeElement = document.createElement('div');
        readingTimeElement.className = 'tiptop-reading-time';
        readingTimeElement.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <polyline points="12 6 12 12 16 14"></polyline>
            </svg>
            <span><strong>Reading time:</strong> <span style="font-weight: 600; color: #2980b9;">${data.readingTime.minutes} min</span> (${data.readingTime.words} words)</span>
        `;

        // Insert the reading time at the top of the AI content sections
        const aiContentSections = document.getElementById('tiptop-ai-content-sections');
        if (aiContentSections) {
            aiContentSections.insertBefore(readingTimeElement, aiContentSections.firstChild);
        }
    }

    // Fill in the summary - with typewriter effect only for fresh data
    if (data.summary && data.summary.text) {
        const summaryElement = document.getElementById('tiptop-summary');
        if (summaryElement) {
            // Decode HTML entities before displaying
            let decodedText = decodeHtml(data.summary.text);

            // Remove markdown headers (###) from the text
            decodedText = decodedText.replace(/^###\s+/gm, '');
            decodedText = decodedText.replace(/^##\s+/gm, '');
            decodedText = decodedText.replace(/^#\s+/gm, '');

            // Check if the text appears to be truncated (ends with an incomplete sentence)
            const lastChar = decodedText.charAt(decodedText.length - 1);

            // If the text doesn't end with proper sentence-ending punctuation
            if (!lastChar.match(/[.!?]/)) {
                // Find the last complete sentence
                const sentenceRegex = /[^.!?]*[.!?]/g;
                const sentences = decodedText.match(sentenceRegex) || [];

                if (sentences.length > 0) {
                    // Use only complete sentences
                    decodedText = sentences.join('');
                    console.log('Client-side truncation detected, adjusted to end with complete sentence');
                } else {
                    // If we can't find complete sentences, add ellipsis
                    decodedText += '...';
                }
            }

            // Also check for incomplete ellipsis
            if (decodedText.endsWith('..') && !decodedText.endsWith('...')) {
                decodedText += '.';
            }

            if (isFromCache) {
                // For cached data, just set the text directly without animation
                summaryElement.style.opacity = '0';
                summaryElement.textContent = decodedText;
                // Fade in after setting content
                setTimeout(() => {
                    summaryElement.style.opacity = '1';
                }, 150);
            } else {
                // For fresh data, use the typewriter effect
                // The typewriter effect function will handle the opacity
                await typewriterEffect(summaryElement, decodedText, 10); // Faster typing speed
            }
        }
    } else {
        // Hide the summary section if no summary data
        const summarySection = document.getElementById('tiptop-summary-section');
        if (summarySection) {
            summarySection.style.display = 'none';
        }
    }

    // Fill in key takeaways with fade-in effect
    if (data.keyTakeaways && data.keyTakeaways.length > 0) {
        const takeawaysElement = document.getElementById('tiptop-takeaways');
        if (takeawaysElement) {
            let takeawaysHtml = '';
            data.keyTakeaways.forEach(takeaway => {
                takeawaysHtml += `<li>${decodeHtml(takeaway)}</li>`;
            });
            takeawaysElement.innerHTML = takeawaysHtml;
            takeawaysElement.style.opacity = '0';
            setTimeout(() => {
                takeawaysElement.style.opacity = '1';
            }, 150);
        }
    } else {
        // Hide the takeaways section if no takeaways data
        const takeawaysSection = document.getElementById('tiptop-takeaways-section');
        if (takeawaysSection) {
            takeawaysSection.style.display = 'none';
        }
    }

    // Fill in combined resources and tips with fade-in effect
    if (data.resources && data.resources.length > 0) {
        const resourcesTipsElement = document.getElementById('tiptop-resources-tips');
        if (resourcesTipsElement) {
            let resourcesHtml = '';
            data.resources.forEach(resource => {
                // Check if it's a tip or a link based on the title format
                const isRelatedLink = resource.title.startsWith('Related:');

                // Create a card-like item similar to the Related Resources section
                resourcesHtml += `
                <li class="tiptop-tip-item">
                    <div class="tiptop-tip-content">
                        <div class="tiptop-tip-title">${decodeHtml(resource.title)}</div>
                        <div class="tiptop-tip-description">${decodeHtml(resource.description)}</div>`;

                if (resource.url) {
                    // Get domain from URL
                    let domain = '';
                    let urlObj;
                    try {
                        urlObj = new URL(resource.url);
                        domain = urlObj.hostname;
                    } catch (e) {
                        console.error('Failed to parse URL:', e);
                    }

                    // Create favicon URL using Google's favicon service (higher quality)
                    const faviconUrl = domain ? `https://www.google.com/s2/favicons?domain=${domain}&sz=64` : '';

                    // Create URL for site screenshot as a better fallback
                    const screenshotUrl = domain ? `https://image.thum.io/get/width/64/crop/64/noanimate/https://${domain}` : '';

                    // Generate unique IDs for SVG elements to prevent conflicts
                    const uniqueId = Math.random().toString(36).substring(2, 10);

                    // Better fallback SVG icons with gradients, colors and backgrounds
                    const fallbackIconSvg = isRelatedLink ?
                        // Resource icon - book with gradient and background
                        `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" style="vertical-align: middle; margin-right: 5px;">
                            <defs>
                                <linearGradient id="resourceGradient-${uniqueId}" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" stop-color="#4facfe"/>
                                    <stop offset="100%" stop-color="#00f2fe"/>
                                </linearGradient>
                                <linearGradient id="resourceBgGradient-${uniqueId}" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" stop-color="#4facfe" stop-opacity="0.2"/>
                                    <stop offset="100%" stop-color="#00f2fe" stop-opacity="0.1"/>
                                </linearGradient>
                            </defs>
                            <rect width="20" height="20" x="2" y="2" rx="4" fill="url(#resourceBgGradient-${uniqueId})"/>
                            <path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20" stroke="url(#resourceGradient-${uniqueId})"/>
                            <path d="M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z" stroke="url(#resourceGradient-${uniqueId})"/>
                            <line x1="8" y1="7" x2="16" y2="7" stroke="url(#resourceGradient-${uniqueId})"/>
                            <line x1="8" y1="11" x2="16" y2="11" stroke="url(#resourceGradient-${uniqueId})"/>
                            <line x1="8" y1="15" x2="12" y2="15" stroke="url(#resourceGradient-${uniqueId})"/>
                        </svg>` :
                        // Source icon - link with gradient and background
                        `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" style="vertical-align: middle; margin-right: 5px;">
                            <defs>
                                <linearGradient id="linkGradient-${uniqueId}" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" stop-color="#6a11cb"/>
                                    <stop offset="100%" stop-color="#2575fc"/>
                                </linearGradient>
                                <linearGradient id="linkBgGradient-${uniqueId}" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" stop-color="#6a11cb" stop-opacity="0.2"/>
                                    <stop offset="100%" stop-color="#2575fc" stop-opacity="0.1"/>
                                </linearGradient>
                            </defs>
                            <rect width="20" height="20" x="2" y="2" rx="4" fill="url(#linkBgGradient-${uniqueId})"/>
                            <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71" stroke="url(#linkGradient-${uniqueId})"/>
                            <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71" stroke="url(#linkGradient-${uniqueId})"/>
                        </svg>`;

                    // Simplified approach with direct SVG icons
                    let iconHtml;

                    if (domain) {
                        // Use a colored SVG with the site's initial letter
                        const color1 = getRandomColor(domain);
                        const color2 = getRandomColor(domain, 40);

                        // Generate unique IDs for domain-based SVGs
                        const domainUniqueId = Math.random().toString(36).substring(2, 10);

                        // Debug log to confirm code execution
                        console.log('TipTop: Creating icon for domain', domain, 'with colors', color1, color2);

                        iconHtml = isRelatedLink ?
                            // Resource icon (book) with domain-based color
                            `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" style="vertical-align: middle; margin-right: 5px;">
                                <defs>
                                    <linearGradient id="grad-${domainUniqueId}" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" stop-color="${color1}"/>
                                        <stop offset="100%" stop-color="${color2}"/>
                                    </linearGradient>
                                </defs>
                                <rect width="20" height="20" x="2" y="2" rx="4" fill="url(#grad-${domainUniqueId})" opacity="0.2"/>
                                <text x="12" y="16" font-family="Arial" font-size="10" font-weight="bold" fill="${color1}" text-anchor="middle">${domain.charAt(0).toUpperCase()}</text>
                                <path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20" stroke="${color1}"/>
                                <path d="M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z" stroke="${color1}"/>
                            </svg>` :
                            // Source icon (link) with domain-based color
                            `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" style="vertical-align: middle; margin-right: 5px;">
                                <defs>
                                    <linearGradient id="grad-${domainUniqueId}" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" stop-color="${color1}"/>
                                        <stop offset="100%" stop-color="${color2}"/>
                                    </linearGradient>
                                </defs>
                                <rect width="20" height="20" x="2" y="2" rx="4" fill="url(#grad-${domainUniqueId})" opacity="0.2"/>
                                <text x="12" y="16" font-family="Arial" font-size="10" font-weight="bold" fill="${color1}" text-anchor="middle">${domain.charAt(0).toUpperCase()}</text>
                                <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71" stroke="${color1}"/>
                                <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71" stroke="${color1}"/>
                            </svg>`;
                    } else {
                        // No domain, use default SVG fallback
                        iconHtml = fallbackIconSvg;
                    }

                    resourcesHtml += `
                        <div class="tiptop-tip-link">
                            <a href="${decodeHtml(resource.url)}" target="_blank" rel="noopener noreferrer">
                                ${iconHtml}
                                ${isRelatedLink ? 'Visit Resource' : 'View Source'}
                            </a>
                        </div>`;
                }

                resourcesHtml += `
                    </div>
                </li>`;
            });
            resourcesTipsElement.innerHTML = resourcesHtml;
            resourcesTipsElement.style.opacity = '0';
            setTimeout(() => {
                resourcesTipsElement.style.opacity = '1';
            }, 200);
        }
    } else {
        // Hide the resources & tips section if no data
        const resourcesTipsSection = document.getElementById('tiptop-resources-tips-section');
        if (resourcesTipsSection) {
            resourcesTipsSection.style.display = 'none';
        }
    }

    // Fill in affiliate resources with fade-in effect
    if (data.affiliateResources && data.affiliateResources.length > 0) {
        const resourcesElement = document.getElementById('tiptop-resources');
        if (resourcesElement) {
            let resourcesHtml = '';
            data.affiliateResources.forEach(resource => {
                resourcesHtml += `
                <li class="tiptop-resource">
                    <a href="${decodeHtml(resource.url)}" target="_blank" rel="noopener noreferrer">
                        <img src="${decodeHtml(resource.imageUrl)}" alt="${decodeHtml(resource.title)}" class="tiptop-resource-image">
                        <div class="tiptop-resource-content">
                            <div class="tiptop-resource-title">${decodeHtml(resource.title)}</div>
                            <div class="tiptop-resource-description">${decodeHtml(resource.description)}</div>
                            <div class="tiptop-resource-price">${decodeHtml(resource.price)}</div>
                        </div>
                    </a>
                </li>`;
            });
            resourcesElement.innerHTML = resourcesHtml;
            resourcesElement.style.opacity = '0';
            setTimeout(() => {
                resourcesElement.style.opacity = '1';
            }, 400); // Slightly longer delay for a staggered effect
        }
    } else {
        // Hide the resources section if no data
        const resourcesSection = document.getElementById('tiptop-resources-section');
        if (resourcesSection) {
            resourcesSection.style.display = 'none';
        }
    }

    // If no AI content is available at all, show a message
    if ((!data.summary || !data.summary.text) &&
        (!data.keyTakeaways || data.keyTakeaways.length === 0) &&
        (!data.resources || data.resources.length === 0) &&
        (!data.affiliateResources || data.affiliateResources.length === 0)) {

        displayAIContentError("No AI content could be generated for this page. Try another page or check back later.");
    }
}

function updatePanelContent(html) {
    if (tiptopPanel) {
        const contentDiv = document.getElementById('tiptop-content');
        if (contentDiv) {
            contentDiv.innerHTML = html;
        }
    }
}

// Improved typewriter effect function with better handling of long text
function typewriterEffect(element, text, speed = 20, startDelay = 0) {
    return new Promise(resolve => {
        // Clear the element first
        element.textContent = '';

        // Make sure the element is visible during typing
        element.style.opacity = '1';

        // Add a blinking cursor
        const cursor = document.createElement('span');
        cursor.className = 'tiptop-cursor';
        cursor.textContent = '|';
        cursor.style.animation = 'tiptop-blink 1s step-end infinite';
        element.appendChild(cursor);

        // For very long text, use a faster approach to avoid performance issues
        const isVeryLong = text.length > 1000;

        if (isVeryLong) {
            // For very long text, split into chunks and add them with small delays
            const chunkSize = 200;
            const chunks = [];

            // Split text into chunks
            for (let i = 0; i < text.length; i += chunkSize) {
                chunks.push(text.substring(i, i + chunkSize));
            }

            setTimeout(() => {
                let chunkIndex = 0;

                const addNextChunk = () => {
                    if (chunkIndex < chunks.length) {
                        const textNode = document.createTextNode(chunks[chunkIndex]);
                        element.insertBefore(textNode, cursor);
                        chunkIndex++;

                        // Add next chunk after a small delay
                        setTimeout(addNextChunk, 100);
                    } else {
                        // All chunks added, remove cursor and resolve
                        element.removeChild(cursor);
                        resolve();
                    }
                };

                // Start adding chunks
                addNextChunk();
            }, startDelay);
        } else {
            // For shorter text, use the original character-by-character approach
            setTimeout(() => {
                let i = 0;
                const timer = setInterval(() => {
                    if (i < text.length) {
                        // Create a text node with the next character
                        const char = document.createTextNode(text.charAt(i));
                        element.insertBefore(char, cursor);
                        i++;
                    } else {
                        clearInterval(timer);
                        // Remove the cursor after typing is complete
                        element.removeChild(cursor);
                        resolve();
                    }
                }, speed);
            }, startDelay);
        }
    });
}

// Simple HTML escaping function (Reformatted)
function escapeHtml(unsafe) {
    if (!unsafe) {
        return '';
    }
    // Ensure string conversion in case input is not a string
    const safeString = String(unsafe);
    return safeString
        .replace(/&/g, "&amp;")
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;")
        .replace(/"/g, "&quot;") // Ensure this line uses &quot;
        .replace(/'/g, "&#039;");
}

// Function to decode HTML entities
function decodeHtml(html) {
    if (!html) {
        return '';
    }
    const txt = document.createElement('textarea');
    txt.innerHTML = html;
    return txt.value;
}

// Function to generate a consistent color based on domain name
function getRandomColor(domain, offset = 0) {
    if (!domain) return '#3498db';

    // Create a hash from the domain string
    let hash = 0;
    for (let i = 0; i < domain.length; i++) {
        hash = domain.charCodeAt(i) + ((hash << 5) - hash);
    }

    // Add the offset to create a related but different color
    hash += offset;

    // Convert to a good-looking hex color
    const hue = ((hash % 360) + 360) % 360; // 0-359 degrees
    const saturation = 70 + (hash % 20); // 70-89%
    const lightness = 45 + (hash % 15); // 45-59%

    return `hsl(${hue}, ${saturation}%, ${lightness}%)`;
}


// Function to reset button position to default (bottom right)
function resetButtonPosition() {
    if (!tiptopButton) return;

    // Clear any saved position
    chrome.storage.local.remove(['tiptopButtonPosition'], function() {
        console.log('Button position reset');

        // Set to bottom right
        tiptopButton.style.right = '20px';
        tiptopButton.style.bottom = '20px';
        tiptopButton.style.top = '';
        tiptopButton.style.left = '';

        // Update panel position if it's visible
        if (tiptopPanel && tiptopPanel.style.display !== 'none') {
            updatePanelPosition();
        }
    });
}

// Function to ensure button is visible in viewport
function ensureButtonIsVisible() {
    if (!tiptopButton) return;

    // Check if button is using left/top positioning (has been dragged)
    if (tiptopButton.style.left && tiptopButton.style.top) {
        const buttonRect = tiptopButton.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;

    // Check if any part of the button is outside the viewport
    if (buttonRect.right < 0 || 
        buttonRect.bottom < 0 || 
        buttonRect.left > viewportWidth || 
        buttonRect.top > viewportHeight ||
        buttonRect.left < 0 || 
        buttonRect.top < 0 || 
        buttonRect.right > viewportWidth || 
        buttonRect.bottom > viewportHeight) {
        console.log('Button partially or completely outside viewport, resetting to default position');
        resetButtonPosition();
    }
    }
}

// --- Social Features ---

// Function to initialize AI features
function initializeAIFeatures() {
    const aiToggle = document.getElementById('tiptop-ai-toggle');
    const aiContentSections = document.getElementById('tiptop-ai-content-sections');
    const collaborationControls = document.querySelector('.tiptop-collaboration-controls');
    const socialToggle = document.getElementById('tiptop-social-toggle');

    if (!aiToggle || !aiContentSections) {
        console.error('AI toggle elements not found');
        return;
    }

    // Toggle AI features
    aiToggle.addEventListener('change', function() {
        if (aiContentSections) {
            aiContentSections.style.display = this.checked ? 'block' : 'none';
        }

        if (this.checked) {
            // AI is toggled on - fetch AI data and make sure social features are off
            console.log('AI toggle enabled - fetching AI data');
            fetchAIData(); // Fetch AI data only when toggle is enabled

            if (socialToggle) {
                socialToggle.checked = false;

                // Hide social controls and loading
                if (collaborationControls) {
                    collaborationControls.style.display = 'none';
                }
                const collaborationLoading = document.getElementById('tiptop-collaboration-loading');
                if (collaborationLoading) {
                    collaborationLoading.style.display = 'none';
                }

                // Toggle social features off in the social client
                if (window.TipTopSocial && window.TipTopSocial.toggleSocialFeatures) {
                    window.TipTopSocial.toggleSocialFeatures(false);
                }
            }
        } else {
            // AI is toggled off - turn on social features
            console.log('AI toggle disabled - stopping AI data fetch');

            if (socialToggle) {
                socialToggle.checked = true;

                // Show social loading state initially, controls will show when connected
                const collaborationLoading = document.getElementById('tiptop-collaboration-loading');
                if (collaborationLoading) {
                    collaborationLoading.style.display = 'block';
                }
                if (collaborationControls) {
                    collaborationControls.style.display = 'none'; // Will be shown when connected
                }

                // Toggle social features on in the social client
                if (window.TipTopSocial && window.TipTopSocial.toggleSocialFeatures) {
                    window.TipTopSocial.toggleSocialFeatures(true);
                }
            }
        }
    });
}

// Initialize AI interaction features
function initializeAIInteraction() {
    const aiQuestionInput = document.getElementById('tiptop-ai-question');
    const aiAskButton = document.getElementById('tiptop-ai-ask');

    if (!aiQuestionInput || !aiAskButton) {
        console.error('AI interaction elements not found');
        return;
    }

    // Add event listener for the Ask button
    aiAskButton.addEventListener('click', function() {
        sendAIQuestion();
    });

    // Add event listener for Enter key in the input field
    aiQuestionInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            sendAIQuestion();
        }
    });

    // Function to send the question to the AI
    function sendAIQuestion() {
        const question = aiQuestionInput.value.trim();
        if (!question) {
            console.log('Question is empty, not sending');
            return;
        }

        console.log('Sending question to AI:', question);

        // Show loading indicator
        const responseContainer = document.getElementById('tiptop-ai-response-container');
        const aiResponse = document.getElementById('tiptop-ai-response');

        if (responseContainer && aiResponse) {
            // Clear previous response
            aiResponse.textContent = '';

            // Show the response container if it's hidden
            responseContainer.style.display = 'block';

            // Add loading indicator
            aiResponse.innerHTML = `
                <div class="tiptop-ai-loading">
                    <span>Thinking</span>
                    <span class="dot"></span>
                    <span class="dot"></span>
                    <span class="dot"></span>
                </div>
            `;

            // Get the current URL
            const currentUrl = window.location.href;

            // Check if we have a cached response for this question on this URL
            const cacheKey = `${currentUrl}:${question}`;
            if (cachedAIResponses[cacheKey]) {
                console.log('Using cached AI response for question:', question);
                // Display the cached response without typewriter effect
                displayAIResponse(cachedAIResponses[cacheKey], true);
                return;
            }

            // Send message to background script
            chrome.runtime.sendMessage({
                type: 'ASK_AI_QUESTION',
                data: {
                    question: question,
                    url: currentUrl
                }
            }, function(response) {
                if (chrome.runtime.lastError) {
                    console.error('Error sending/receiving message:', chrome.runtime.lastError);
                    displayAIResponse('Sorry, there was an error processing your question. Please try again later.', false);
                    return;
                }

                console.log('Received AI response:', response);

                if (response && response.data && response.data.answer) {
                    // Cache the response
                    cachedAIResponses[cacheKey] = response.data.answer;

                    // Limit cache size to prevent memory issues
                    const cacheKeys = Object.keys(cachedAIResponses);
                    if (cacheKeys.length > 20) {
                        delete cachedAIResponses[cacheKeys[0]];
                    }

                    // Display the AI response with typewriter effect for fresh responses
                    displayAIResponse(response.data.answer, false);
                } else if (response && response.error) {
                    // Display specific error message from the server
                    console.error('AI response error:', response.error);
                    displayAIResponse(`I'm sorry, I couldn't process your question. Error: ${response.error}`, false);
                } else {
                    // Display generic error message
                    displayAIResponse('Sorry, I couldn\'t answer that question. Please try again with a different question.', false);
                }

                // Clear the input field after sending
                aiQuestionInput.value = '';
            });
        }
    }

    // Function to display the AI response
    async function displayAIResponse(text, isFromCache = false) {
        const aiResponse = document.getElementById('tiptop-ai-response');
        if (aiResponse) {
            if (isFromCache) {
                // For cached responses, just set the text directly without animation
                aiResponse.style.opacity = '0';
                aiResponse.textContent = text;
                // Fade in after setting content
                setTimeout(() => {
                    aiResponse.style.opacity = '1';
                }, 150);
            } else {
                // For fresh responses, use the typewriter effect
                // The typewriter effect function will handle the opacity
                await typewriterEffect(aiResponse, text, 15);
            }
        }
    }
}

// Initialize social features after panel is displayed
// Track current URL for state management
let currentURL = window.location.href;

// Function to reset social state on URL change
function resetSocialState() {
    console.log('Resetting social state for new URL');

    // Reset active users and messages
    if (window.TipTopSocial) {
        window.TipTopSocial.activeUsers = [];
        window.TipTopSocial.messageQueue = [];
    }

    // Clear UI elements
    const userList = document.getElementById('tiptop-user-list');
    if (userList) {
        userList.innerHTML = '<div class="tiptop-no-users">No other users viewing this page</div>';
    }

    const chatLog = document.getElementById('tiptop-chat-log');
    if (chatLog) {
        chatLog.innerHTML = '';
    }
}

// Enhanced function to reset social state for navigation (called by background script)
function resetSocialStateForNavigation(data = {}) {
    console.log('🔄 Resetting social state for navigation (UI only)', data);

    // ONLY clear UI elements, do NOT clear storage or interfere with server messages
    // Messages should persist on the server for all users viewing that URL

    // Clear chat messages from UI immediately (but NOT from storage)
    const chatLog = document.getElementById('tiptop-chat-log');
    if (chatLog) {
        chatLog.innerHTML = '';
        console.log('Cleared chat log UI for navigation (messages persist for other users)');
    }

    const historyContent = document.getElementById('tiptop-history-content');
    if (historyContent) {
        historyContent.innerHTML = '';
        console.log('Cleared history content UI for navigation');
    }

    // Clear users list
    const usersList = document.getElementById('tiptop-users-list');
    if (usersList) {
        usersList.innerHTML = '';
        console.log('Cleared users list UI for navigation');
    }

    // Notify social client about navigation reset
    if (window.TipTopSocial && window.TipTopSocial.handleNavigationReset) {
        console.log('🔄 Notifying social client about navigation reset');
        window.TipTopSocial.handleNavigationReset(data);
    }
}

    // Clear all social state
    if (window.TipTopSocial) {
        // Clear processed message IDs to allow re-processing of messages for new page
        if (window.TipTopSocial.processedMessageIds) {
            window.TipTopSocial.processedMessageIds.clear();
            console.log('Cleared processed message IDs');
        }

        // Reset active users and message queue
        window.TipTopSocial.activeUsers = [];
        window.TipTopSocial.messageQueue = [];
        console.log('Reset active users and message queue');

        // Force reconnection to get fresh state for new page
        if (window.TipTopSocial.connectThroughBackground) {
            setTimeout(() => {
                console.log('Reconnecting social client for new page');
                window.TipTopSocial.connectThroughBackground();
            }, 500);
        }
    }

    // Clear all UI elements
    const userList = document.getElementById('tiptop-user-list');
    if (userList) {
        userList.innerHTML = '<div class="tiptop-no-users">No other users viewing this page</div>';
        console.log('Cleared user list UI');
    }

    // Clear any cached messages for the previous URL
    const currentUrl = window.location.href;
    console.log('Navigation reset complete for URL:', currentUrl);

function initializeSocialFeatures() {
    // Get current URL and check if it has changed
    const newURL = window.location.href;
    if (newURL !== currentURL) {
        currentURL = newURL;
        resetSocialState();
        return;
    }

    // Get elements
    const socialToggle = document.getElementById('tiptop-social-toggle');
    const chatInputs = document.querySelectorAll('[id^="tiptop-chat-input"]');
    const chatSendButtons = document.querySelectorAll('[id^="tiptop-chat-send"]');
    const tabButtons = document.querySelectorAll('.tiptop-tab-button');

    if (!socialToggle || chatInputs.length === 0 || chatSendButtons.length === 0) {
        console.error('Social UI elements not found');
        return;
    }



    // Toggle social features
    socialToggle.addEventListener('change', function() {
        if (window.TipTopSocial) {
            window.TipTopSocial.toggleSocialFeatures(this.checked);

            // Show/hide social UI based on toggle state
            const collaborationControls = document.querySelector('.tiptop-collaboration-controls');
            const collaborationLoading = document.getElementById('tiptop-collaboration-loading');
            const aiContentSections = document.getElementById('tiptop-ai-content-sections');
            const aiToggle = document.getElementById('tiptop-ai-toggle');

            if (this.checked) {
                // Show loading state initially, controls will show when connected
                if (collaborationLoading) {
                    collaborationLoading.style.display = 'block';
                }
                if (collaborationControls) {
                    collaborationControls.style.display = 'none'; // Will be shown when connected
                }

                // If AI toggle is on, turn it off
                if (aiToggle) {
                    aiToggle.checked = false;

                    // Hide AI content sections
                    if (aiContentSections) {
                        aiContentSections.style.display = 'none';
                    }
                }
            } else {
                // Hide all social UI
                if (collaborationControls) {
                    collaborationControls.style.display = 'none';
                }
                if (collaborationLoading) {
                    collaborationLoading.style.display = 'none';
                }

                // Turn on AI toggle and show AI content sections
                if (aiToggle && aiContentSections) {
                    aiToggle.checked = true;
                    aiContentSections.style.display = 'block';
                    // Fetch AI data when AI toggle is programmatically enabled
                    console.log('Social toggle disabled - enabling AI and fetching AI data');
                    fetchAIData();
                }
            }
        }
    });

    // Send chat message
    function sendChatMessage() {
        console.log('Send chat message function called');

        // Find the active chat input (focused or with content)
        let activeInput = null;
        for (const input of chatInputs) {
            if (document.activeElement === input || input.value.trim()) {
                activeInput = input;
                break;
            }
        }

        // If no active input found, use the first one
        if (!activeInput && chatInputs.length > 0) {
            activeInput = chatInputs[0];
        }

        if (!activeInput) {
            console.error('No chat input found');
            return;
        }

        const message = activeInput.value.trim();
        console.log('Message content:', message);

        if (!message) {
            console.log('Message is empty, not sending');
            return;
        }

        if (!window.TipTopSocial) {
            console.error('TipTopSocial object not available');
            showNotification('Chat functionality not available', 'error');
            return;
        }

        console.log('Calling TipTopSocial.sendChatMessage');
        const success = window.TipTopSocial.sendChatMessage(message);
        console.log('Send result:', success);

        if (success) {
            activeInput.value = '';
        } else {
            showNotification('Failed to send message. Please try again.', 'error');
        }
    }

    console.log('Adding event listeners to chat send buttons');
    chatSendButtons.forEach(button => {
        button.addEventListener('click', function() {
            console.log('Chat send button clicked');
            sendChatMessage();
        });
    });

    console.log('Adding event listeners to chat inputs for Enter key');
    chatInputs.forEach(input => {
        input.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                console.log('Enter key pressed in chat input');
                sendChatMessage();
            }
        });
    });



    // Chat history toggle buttons
    const showHistoryButton = document.getElementById('tiptop-show-history');
    const hideHistoryButton = document.getElementById('tiptop-hide-history');
    const chatHistorySection = document.getElementById('tiptop-chat-history');

    if (showHistoryButton && hideHistoryButton && chatHistorySection) {
        showHistoryButton.addEventListener('click', function() {
            chatHistorySection.style.display = 'block';
            showHistoryButton.style.display = 'none';
            loadChatHistory();
        });

        hideHistoryButton.addEventListener('click', function() {
            chatHistorySection.style.display = 'none';
            showHistoryButton.style.display = 'inline-flex';
        });
    }

    // Load chat history initially
    loadChatHistory();



    // Tab switching
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all buttons and content
            tabButtons.forEach(btn => btn.classList.remove('active'));
            document.querySelectorAll('.tiptop-tab-content').forEach(content => {
                content.classList.remove('active');
            });

            // Add active class to clicked button and corresponding content
            this.classList.add('active');
            const tabName = this.getAttribute('data-tab');
            document.getElementById(`tiptop-tab-${tabName}`).classList.add('active');

            // If switching to chat tab, force scroll to bottom
            if (tabName === 'chat' && window.TipTopSocial && window.TipTopSocial.forceScrollChatOnReopen) {
                console.log('Switching to chat tab, forcing scroll to bottom');
                setTimeout(() => {
                    window.TipTopSocial.forceScrollChatOnReopen();
                }, 100);
            }
        });
    });

    // Initialize TipTop social client
    if (window.TipTopSocial) {
        window.TipTopSocial.initialize();

        // Default to enabled (true) when initially opened - prioritize social features
        socialToggle.checked = true;

        // Toggle social features on by default
        if (window.TipTopSocial.toggleSocialFeatures) {
            window.TipTopSocial.toggleSocialFeatures(true);
        }

        // Store current URL for future comparisons
        window.TipTopSocial.currentURL = window.location.href;

    // Load messages now that UI is ready
    if (window.TipTopSocial.loadMessagesWhenReady) {
        window.TipTopSocial.loadMessagesWhenReady();
    }
    
    // Set up MutationObserver to detect URL changes
    const observer = new MutationObserver(() => {
        const newURL = window.location.href;
        if (newURL !== currentURL) {
            console.log('🔄 URL change detected:', currentURL, '->', newURL);
            const oldURL = currentURL;
            // Store current URL for future comparisons
            currentURL = newURL;

            // Clear chat messages UI when navigating to a new page (but preserve server messages)
            const historyContent = document.getElementById('tiptop-history-content');
            if (historyContent) {
                historyContent.innerHTML = '';
                console.log('Cleared chat messages UI for new page (messages persist on server)');
            }

            // Reset social state for new page (UI only)
            resetSocialStateForNavigation({ oldUrl: oldURL, newUrl: newURL });

            // Request fresh messages for the new URL after a short delay
            setTimeout(() => {
                if (window.TipTopSocial && window.TipTopSocial.loadMessagesWhenReady) {
                    console.log('Loading messages for new URL:', newURL);
                    window.TipTopSocial.loadMessagesWhenReady();
                }
            }, 500);
        }
    });
    
    // Observe the entire document for URL changes
    observer.observe(document, {
        childList: true,
        subtree: true,
        attributes: true
    });

        const collaborationControls = document.querySelector('.tiptop-collaboration-controls');
        const collaborationLoading = document.getElementById('tiptop-collaboration-loading');
        const aiContentSections = document.getElementById('tiptop-ai-content-sections');
        const aiToggle = document.getElementById('tiptop-ai-toggle');

        // Show loading state initially, hide tabs until connected
        if (collaborationLoading) {
            collaborationLoading.style.display = 'block';
        }
        if (collaborationControls) {
            collaborationControls.style.display = 'none';
        }

        // Hide AI content sections by default and disable AI toggle
        if (aiContentSections) {
            aiContentSections.style.display = 'none';
        }
        if (aiToggle) {
            aiToggle.checked = false;
        }

        // Save the enabled state to storage
        chrome.storage.sync.set({ tiptopSocialEnabled: true }, function() {
            if (chrome.runtime.lastError) {
                console.error('Error saving social preference to sync storage:', chrome.runtime.lastError);
                // Fall back to local storage
                chrome.storage.local.set({ tiptopSocialEnabled: true });
            }
        });
    } else {
        console.error('TipTop social client not available');
    }
}

// Save a note for the current page
function saveNote(noteText) {
    const currentUrl = window.location.href;
    const pageTitle = document.title;
    const timestamp = new Date().toISOString();

    // Generate a unique note ID
    const noteId = `note_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    // Create a URL-safe key for the current URL
    const urlKey = btoa(currentUrl).replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');
    const notesKey = `tiptop_notes_${urlKey}`;

    // Get user name from sync storage first, then fall back to local
    chrome.storage.sync.get(['tiptopUserName'], function(syncResult) {
        if (chrome.runtime.lastError) {
            // Fall back to local storage
            getUserNameFromLocal();
            return;
        }

        if (syncResult.tiptopUserName) {
            createAndSaveNote(syncResult.tiptopUserName);
        } else {
            // Not found in sync, try local
            getUserNameFromLocal();
        }
    });

    function getUserNameFromLocal() {
        chrome.storage.local.get(['tiptopUserName'], function(result) {
            const userName = result.tiptopUserName || 'Anonymous User';
            createAndSaveNote(userName);
        });
    }

    function createAndSaveNote(userName) {
        // Create note object
        const note = {
            url: currentUrl,
            title: pageTitle,
            text: noteText,
            userName: userName,
            timestamp: timestamp,
            noteId: noteId,
            userId: window.TipTopSocial ? window.TipTopSocial._getUserId() : null,
            type: 'note'
        };

        // Try to send note to server for synchronization
        let sentToServer = false;
        if (window.TipTopSocial) {
            sentToServer = window.TipTopSocial.sendNote(noteText);
        }

        // Save to sync storage first
        saveToSyncStorage(note, sentToServer);
    }

    function saveToSyncStorage(note, sentToServer) {
        chrome.storage.sync.get([notesKey], function(result) {
            if (chrome.runtime.lastError) {
                console.error('Error accessing sync storage for notes:', chrome.runtime.lastError);
                // Fall back to local storage
                saveToLocalStorage(note, sentToServer);
                return;
            }

            let urlNotes = result[notesKey] || [];

            // Add new note
            urlNotes.push(note);

            // Limit to 20 notes per URL for sync storage
            if (urlNotes.length > 20) {
                urlNotes = urlNotes.slice(-20);
            }

            // Save updated notes
            const storageData = {};
            storageData[notesKey] = urlNotes;

            chrome.storage.sync.set(storageData, function() {
                if (chrome.runtime.lastError) {
                    console.error('Error saving notes to sync storage:', chrome.runtime.lastError);
                    // Fall back to local storage
                    saveToLocalStorage(note, sentToServer);
                    return;
                }

                if (sentToServer) {
                    showNotification('Note saved and shared with others', 'success');
                } else {
                    showNotification('Note saved', 'success');
                }

                // Refresh notes display
                loadNotes();

                // Also save to local storage as backup
                saveToLocalStorage(note, null, false);
            });
        });
    }

    function saveToLocalStorage(note, sentToServer, showNotifications = true) {
        chrome.storage.local.get([notesKey], function(result) {
            if (chrome.runtime.lastError) {
                console.error('Error accessing local storage for notes:', chrome.runtime.lastError);
                if (showNotifications) {
                    showNotification('Failed to save note', 'error');
                }
                return;
            }

            let urlNotes = result[notesKey] || [];

            // Add new note
            urlNotes.push(note);

            // Limit to 50 notes per URL for local storage
            if (urlNotes.length > 50) {
                urlNotes = urlNotes.slice(-50);
            }

            // Save updated notes
            const storageData = {};
            storageData[notesKey] = urlNotes;

            chrome.storage.local.set(storageData, function() {
                if (chrome.runtime.lastError) {
                    console.error('Error saving notes to local storage:', chrome.runtime.lastError);
                    if (showNotifications) {
                        showNotification('Failed to save note', 'error');
                    }
                    return;
                }

                if (showNotifications) {
                    if (sentToServer) {
                        showNotification('Note saved and shared with others', 'success');
                    } else {
                        showNotification('Note saved locally', 'success');
                    }
                }

                // Refresh notes display if this is the primary save
                if (showNotifications) {
                    loadNotes();
                }
            });
        });
    }
}

// Load notes for the current page
function loadNotes() {
    // This function loads notes and updates the notes display in the settings tab
    const currentUrl = window.location.href;
    const noteContainer = document.getElementById('tiptop-note-container');

    // If there's no note container in the UI, just refresh the chat history
    // which also contains notes
    if (!noteContainer) {
        loadChatHistory();
        return;
    }

    // Create a URL-safe key for the current URL
    const urlKey = btoa(currentUrl).replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');
    const notesKey = `tiptop_notes_${urlKey}`;

    // First try to load from sync storage
    chrome.storage.sync.get([notesKey], function(result) {
        if (chrome.runtime.lastError) {
            console.error('Error accessing sync storage for notes:', chrome.runtime.lastError);
            // Fall back to local storage
            loadFromLocalStorage();
            return;
        }

        const syncNotes = result[notesKey] || [];

        // Also check local storage and combine results
        chrome.storage.local.get([notesKey], function(localResult) {
            let localNotes = [];
            if (!chrome.runtime.lastError) {
                localNotes = localResult[notesKey] || [];
            }

            // Combine notes from both storages
            const allNotes = [...syncNotes, ...localNotes];

            // Remove duplicates by noteId
            const uniqueNotes = [];
            const seenIds = new Set();

            allNotes.forEach(note => {
                if (!seenIds.has(note.noteId)) {
                    seenIds.add(note.noteId);
                    uniqueNotes.push(note);
                }
            });

            // Also refresh the chat history which contains notes
            loadChatHistory();
        });
    });

    // Fallback function to load from local storage only
    function loadFromLocalStorage() {
        // Also check old storage format for backward compatibility
        chrome.storage.local.get(['tiptopNotes', notesKey], function(result) {
            let notes = [];

            // Check new format first
            if (result[notesKey]) {
                notes = result[notesKey];
            }
            // Then check old format
            else if (result.tiptopNotes && result.tiptopNotes[currentUrl]) {
                notes = result.tiptopNotes[currentUrl];

                // Migrate old format to new format
                const storageData = {};
                storageData[notesKey] = notes;
                chrome.storage.local.set(storageData);
            }

            // Also refresh the chat history which contains notes
            loadChatHistory();
        });
    }
}

// Load chat history for the current page
function loadChatHistory() {
    const currentUrl = window.location.href;
    const historyContent = document.getElementById('tiptop-history-content');

    if (!historyContent) return;

    // Create a URL-safe key for the current URL
    const urlKey = btoa(currentUrl).replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');
    const notesKey = `tiptop_notes_${urlKey}`;

    // First try to load from sync storage
    chrome.storage.sync.get([notesKey], function(result) {
        if (chrome.runtime.lastError) {
            console.error('Error accessing sync storage for notes:', chrome.runtime.lastError);
            // Fall back to local storage
            loadFromLocalStorage();
            return;
        }

        const syncNotes = result[notesKey] || [];

        // Also check local storage and combine results
        chrome.storage.local.get([notesKey], function(localResult) {
            let localNotes = [];
            if (!chrome.runtime.lastError) {
                localNotes = localResult[notesKey] || [];
            }

            // Combine notes from both storages
            const allNotes = [...syncNotes, ...localNotes];

            // Remove duplicates by noteId
            const uniqueNotes = [];
            const seenIds = new Set();

            allNotes.forEach(note => {
                if (!seenIds.has(note.noteId)) {
                    seenIds.add(note.noteId);
                    uniqueNotes.push(note);
                }
            });

            displayNotes(uniqueNotes);
        });
    });

    // Fallback function to load from local storage only
    function loadFromLocalStorage() {
        // Also check old storage format for backward compatibility
        chrome.storage.local.get(['tiptopNotes', notesKey], function(result) {
            let notes = [];

            // Check new format first
            if (result[notesKey]) {
                notes = result[notesKey];
            }
            // Then check old format
            else if (result.tiptopNotes && result.tiptopNotes[currentUrl]) {
                notes = result.tiptopNotes[currentUrl];

                // Migrate old format to new format
                const storageData = {};
                storageData[notesKey] = notes;
                chrome.storage.local.set(storageData);
            }

            displayNotes(notes);
        });
    }

    // Function to display notes in the UI
    function displayNotes(notes) {
        if (!notes || notes.length === 0) {
            historyContent.innerHTML = '<div class="tiptop-no-history">No history available for this page yet.</div>';
            return;
        }

        // Sort notes by timestamp (newest first)
        notes.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

        // Create history HTML
        let historyHtml = '';

        notes.forEach(note => {
            // Format the date
            let formattedDate;
            try {
                const date = new Date(note.timestamp);
                formattedDate = date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
            } catch (e) {
                formattedDate = 'Unknown date';
            }

            // Determine if this is a chat message or a note
            const isChat = note.type === 'chat';
            const itemClass = isChat ? 'tiptop-history-item tiptop-history-chat' : 'tiptop-history-item tiptop-history-note';
            const authorClass = isChat ? 'tiptop-history-author tiptop-chat-author' : 'tiptop-history-author tiptop-note-author';

            historyHtml += `
                <div class="${itemClass}">
                    <div class="tiptop-history-item-header">
                        <span class="${authorClass}">${escapeHtml(note.userName || 'Unknown User')}</span>
                        <span class="tiptop-history-time">${formattedDate}</span>
                    </div>
                    <div class="tiptop-history-text">${escapeHtml(note.text || '')}</div>
                </div>
            `;
        });

        historyContent.innerHTML = historyHtml;
    }
}

// Show a notification in the collaboration panel
function showNotification(message, type = 'info') {
    const notificationElement = document.getElementById('tiptop-collaboration-notification');
    if (!notificationElement) return;

    notificationElement.textContent = message;
    notificationElement.className = `tiptop-notification tiptop-notification-${type}`;
    notificationElement.style.display = 'block';

    // Hide after 5 seconds
    setTimeout(() => {
        notificationElement.style.display = 'none';
    }, 5000);
}

// Override the updatePanelContent function to initialize features after content is updated
const originalUpdatePanelContent = updatePanelContent;
updatePanelContent = function(html) {
    originalUpdatePanelContent(html);

    // Initialize social features after panel content is updated
    if (document.getElementById('tiptop-collaboration')) {
        initializeSocialFeatures();
    }

    // Initialize AI features after panel content is updated
    if (document.getElementById('tiptop-ai-content')) {
        initializeAIFeatures();
    }
};

// Function to ensure button exists and is visible
function ensureButtonExists() {
    const existingButton = document.getElementById('tiptop-button');
    if (!existingButton) {
        console.log('🔄 Button missing, recreating...');
        createTipTopButton();
    } else {
        console.log('✅ Button exists');
        tiptopButton = existingButton; // Ensure we have reference
    }

    // Ensure button is visible
    setTimeout(ensureButtonIsVisible, 100);
}

// --- Initialization ---

// Clear AI cache on extension load to prevent automatic AI content display
clearAICache();

// Inject the button when the script loads
// Use a small delay to avoid potential issues with page rendering
setTimeout(function() {
    console.log('🚀 Initializing TipTop extension...');
    ensureButtonExists();

    // Check button periodically to ensure it stays visible
    setInterval(ensureButtonExists, 5000); // Check every 5 seconds
}, 500);

// Listen for window resize to ensure button stays visible
window.addEventListener('resize', ensureButtonIsVisible);

// Add event listener for notes updates
document.addEventListener('tiptop-notes-updated', function() {
    // Refresh the chat history when notes are updated
    loadChatHistory();
});

// Listen for messages (e.g., if triggered by browser action)
chrome.runtime.onMessage.addListener((message) => {
    if (message.type === 'TOGGLE_TIPTOP_UI') {
        toggleTipTopPanel();
    } else if (message.type === 'TIPTOP_RESET_UI') {
        console.log('Received TIPTOP_RESET_UI message, resetting social state', message.data);
        resetSocialStateForNavigation(message.data);
    }
    return false; // No async response needed
});

console.log("TipTop Content Script Initialized.");

// Initialize social client with retry mechanism
function initializeSocialClientSafely() {
    if (window.TipTopSocial && window.TipTopSocial.initialize) {
        console.log("🚀 Initializing TipTop social client for background communication");
        try {
            window.TipTopSocial.initialize();
            console.log("✅ Social client initialized successfully");
        } catch (error) {
            console.error("❌ Error initializing social client:", error);
            // Retry after a delay
            setTimeout(initializeSocialClientSafely, 1000);
        }
    } else {
        console.log("⏳ Social client not ready, retrying in 500ms...");
        setTimeout(initializeSocialClientSafely, 500);
    }
}

// Start social client initialization
initializeSocialClientSafely();
